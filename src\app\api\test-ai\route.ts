import { NextRequest, NextResponse } from 'next/server';
import { testOpenAIConnection, generateAIResponse } from '@/lib/openai-service';
import { createDefaultEnhancedProposal } from '@/types/enhanced-proposal-schema';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing OpenAI connection...');
    
    // Test basic connection
    const connectionTest = await testOpenAIConnection();
    
    if (!connectionTest) {
      return NextResponse.json({
        success: false,
        error: 'OpenAI connection failed',
        message: 'Unable to connect to OpenAI API. Please check your API key and configuration.'
      }, { status: 500 });
    }
    
    // Test proposal generation
    const testProposal = createDefaultEnhancedProposal(
      '<PERSON>',
      'Smith Landscaping',
      'landscaping'
    );
    
    const testPrompt = 'Write a brief overview for this landscaping business proposal.';
    
    console.log('Testing AI response generation...');
    const aiResponse = await generateAIResponse(
      testPrompt,
      testProposal,
      'overview',
      []
    );
    
    return NextResponse.json({
      success: true,
      message: 'OpenAI integration is working correctly',
      connectionTest: true,
      testResponse: {
        prompt: testPrompt,
        response: aiResponse,
        responseLength: aiResponse.length
      }
    });
    
  } catch (error: any) {
    console.error('AI test error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'AI test failed',
      message: error.message,
      details: {
        name: error.name,
        code: error.code,
        type: error.type
      }
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { message, businessName, businessType } = body;
    
    if (!message) {
      return NextResponse.json({
        error: 'Message is required'
      }, { status: 400 });
    }
    
    const testProposal = createDefaultEnhancedProposal(
      'Test Client',
      businessName || 'Test Business',
      businessType || 'general'
    );
    
    const aiResponse = await generateAIResponse(
      message,
      testProposal,
      undefined,
      []
    );
    
    return NextResponse.json({
      success: true,
      response: aiResponse
    });
    
  } catch (error: any) {
    console.error('AI test POST error:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
