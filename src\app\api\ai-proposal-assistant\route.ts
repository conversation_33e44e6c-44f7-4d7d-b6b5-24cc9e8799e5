import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import {
  convertToEnhancedProposal,
  generateSectionPrompt,
  updateProposalSection
} from '@/lib/ai-proposal-helper';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';
import { generateAIResponse } from '@/lib/openai-service';

// Chat configuration
const MAX_CHAT_HISTORY = 20; // Limit chat history to prevent token bloat

export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client that uses cookies (better for server components)
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get current user with the cookie-based client
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('Auth error:', userError);
      return NextResponse.json({ 
        error: 'Authentication failed',
        message: userError.message,
        code: userError.code || 'AUTH_ERROR',
        hint: 'Please make sure you are logged in. Try going to /debug/login-helper to fix your session.'
      }, { status: 401 });
    }
    
    if (!userData.user) {
      console.error('No user found in session');
      return NextResponse.json({ 
        error: 'User not authenticated',
        hint: 'No user found in session. Please log in through /login or try /debug/login-helper.'
      }, { status: 401 });
    }
    
    console.log('User authenticated for AI assistant:', userData.user.email);
    
    // Parse the request body
    const body = await request.json();
    const { proposalId, message, section } = body;
    
    if (!proposalId || !message) {
      return NextResponse.json({ 
        error: 'Missing required fields',
        requiredFields: ['proposalId', 'message']
      }, { status: 400 });
    }
    
    // Get the proposal data
    const { data: proposalData, error: proposalError } = await supabase
      .from('proposals')
      .select('*')
      .eq('id', proposalId)
      .single();
    
    if (proposalError) {
      console.error('Error fetching proposal:', proposalError);
      return NextResponse.json({ 
        error: 'Failed to fetch proposal data',
        message: proposalError.message
      }, { status: 500 });
    }
    
    // Load existing chat history or create new one
    let chatHistory = proposalData.chat_history || [];
    
    // Add the new user message
    chatHistory.push({
      role: 'user',
      content: message,
      timestamp: new Date().toISOString()
    });
    
    // Prepare AI context based on proposal data
    let enhancedProposal: EnhancedProposal;
    
    try {
      // Convert existing proposal data to our enhanced schema
      enhancedProposal = convertToEnhancedProposal(proposalData.proposal_data || {});
    } catch (error) {
      console.error('Error converting proposal data:', error);
      enhancedProposal = convertToEnhancedProposal({
        client_name: proposalData.client_name,
        business_name: proposalData.business_name,
        business_type: proposalData.business_type
      });
    }
    
    // Generate AI response using OpenAI
    let aiResponse = '';
    let updatedProposalData = null;

    try {
      // Check if this is a section-specific request
      let prompt = message;
      if (section) {
        // Generate a targeted prompt for this section
        prompt = generateSectionPrompt(section, enhancedProposal, message);
      }

      console.log('Generating AI response for proposal:', proposalId);
      console.log('Section:', section || 'general');
      console.log('Prompt length:', prompt.length);

      // Call OpenAI service
      aiResponse = await generateAIResponse(prompt, enhancedProposal, section, chatHistory);

      // Update proposal data if a section was specified
      if (section && aiResponse) {
        const updatedProposal = updateProposalSection(enhancedProposal, section, aiResponse);

        // Merge with existing proposal data
        updatedProposalData = {
          ...proposalData.proposal_data,
          ...updatedProposal
        };

        // Update the proposal in the database
        await supabase
          .from('proposals')
          .update({
            proposal_data: updatedProposalData
          })
          .eq('id', proposalId);

        console.log('Updated proposal section:', section);
        console.log('Updated proposal data:', updatedProposalData);

        // Add confirmation to the response
        aiResponse += `\n\n✅ I've updated the ${section} section of your proposal. You can review the changes and edit further if needed.`;
      }
    } catch (error: any) {
      console.error('Error generating AI response:', error);

      // Provide user-friendly error messages
      if (error.message.includes('quota')) {
        aiResponse = `I'm sorry, but we've reached our AI service limit for now. Please try again later or contact support if this persists.`;
      } else if (error.message.includes('rate_limit')) {
        aiResponse = `I'm processing a lot of requests right now. Please wait a moment and try again.`;
      } else if (error.message.includes('timeout')) {
        aiResponse = `The AI service is taking longer than usual. Please try again with a shorter request.`;
      } else {
        aiResponse = `I encountered an issue while processing your request. Please try rephrasing your question or contact support if this continues.`;
      }
    }
    
    // Add AI response to chat history
    chatHistory.push({
      role: 'assistant',
      content: aiResponse,
      timestamp: new Date().toISOString()
    });
    
    // Keep chat history within limit
    if (chatHistory.length > MAX_CHAT_HISTORY) {
      chatHistory = chatHistory.slice(chatHistory.length - MAX_CHAT_HISTORY);
    }
    
    // Update chat history in the database
    await supabase
      .from('proposals')
      .update({ chat_history: chatHistory })
      .eq('id', proposalId);
    
    return NextResponse.json({
      success: true,
      chatHistory: chatHistory,
      updatedProposalData: updatedProposalData,
      sectionUpdated: section || null
    });
  } catch (error: any) {
    console.error('Error in AI proposal assistant:', error);
    return NextResponse.json({ 
      error: 'Failed to process request',
      message: error.message
    }, { status: 500 });
  }
}

// Note: Simulated AI responses have been replaced with real OpenAI integration
// See src/lib/openai-service.ts for the AI implementation