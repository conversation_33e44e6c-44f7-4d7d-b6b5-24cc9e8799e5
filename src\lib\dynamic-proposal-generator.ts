/**
 * Dynamic Proposal Content Generator
 * Analyzes customer information and generates customized proposal content
 */

export interface CustomerAnalysis {
  businessName: string;
  businessType: string;
  currentSituation: string;
  targetMarkets: string[];
  keyGoals: string[];
  challenges: string[];
  recommendedPackage: 'basic' | 'premium';
  confidence: number;
  reasoning: string[];
}

export interface DynamicProposalContent {
  projectTitle: string;
  clientGreeting: string;
  situationAnalysis: string;
  goalsSection: string;
  packageRecommendation: {
    recommended: 'basic' | 'premium';
    reasoning: string;
    whyThisPackage: string[];
  };
  customizedFeatures: {
    basic: string[];
    premium: string[];
  };
  specificSolutions: string[];
  nextSteps: string[];
}

/**
 * Analyzes customer information and generates dynamic proposal content
 */
export function generateDynamicProposal(customerInfo: string): DynamicProposalContent {
  const analysis = analyzeCustomerInfo(customerInfo);
  
  return {
    projectTitle: `Modern Website & Digital Marketing Solution for ${analysis.businessName}`,
    
    clientGreeting: generateClientGreeting(analysis),
    
    situationAnalysis: generateSituationAnalysis(analysis),
    
    goalsSection: generateGoalsSection(analysis),
    
    packageRecommendation: {
      recommended: analysis.recommendedPackage,
      reasoning: analysis.reasoning.join(' '),
      whyThisPackage: generatePackageReasons(analysis)
    },
    
    customizedFeatures: {
      basic: generateBasicFeatures(analysis),
      premium: generatePremiumFeatures(analysis)
    },
    
    specificSolutions: generateSpecificSolutions(analysis),
    
    nextSteps: generateNextSteps(analysis)
  };
}

/**
 * Analyzes customer information to understand their needs
 */
function analyzeCustomerInfo(customerInfo: string): CustomerAnalysis {
  const info = customerInfo.toLowerCase();
  
  // Extract business details
  const businessName = extractBusinessName(customerInfo);
  const businessType = extractBusinessType(info);
  
  // Analyze current situation
  const currentSituation = analyzeSituation(info);
  
  // Extract target markets
  const targetMarkets = extractTargetMarkets(info);
  
  // Extract goals and challenges
  const keyGoals = extractGoals(info);
  const challenges = extractChallenges(info);
  
  // Determine package recommendation
  const packageAnalysis = determinePackage(info, targetMarkets, keyGoals, challenges);
  
  return {
    businessName,
    businessType,
    currentSituation,
    targetMarkets,
    keyGoals,
    challenges,
    recommendedPackage: packageAnalysis.package,
    confidence: packageAnalysis.confidence,
    reasoning: packageAnalysis.reasoning
  };
}

function extractBusinessName(info: string): string {
  // Look for business name patterns
  const patterns = [
    /(?:business|company|firm):\s*([^,\n]+)/i,
    /([A-Z][a-z]+\s+[A-Z][a-z]+)(?:\s+(?:LLC|Inc|Corp))?/,
    /for\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/i
  ];
  
  for (const pattern of patterns) {
    const match = info.match(pattern);
    if (match) return match[1].trim();
  }
  
  return 'Your Business';
}

function extractBusinessType(info: string): string {
  const types = {
    'door': 'door installation',
    'trim': 'trim and millwork',
    'finishing': 'finishing contractor',
    'roofing': 'roofing contractor',
    'plumbing': 'plumbing services',
    'electrical': 'electrical services',
    'hvac': 'HVAC services',
    'landscaping': 'landscaping services',
    'cleaning': 'cleaning services',
    'law': 'legal services',
    'medical': 'medical practice',
    'dental': 'dental practice',
    'restaurant': 'restaurant',
    'bakery': 'bakery',
    'retail': 'retail business'
  };
  
  for (const [keyword, type] of Object.entries(types)) {
    if (info.includes(keyword)) return type;
  }
  
  return 'local business';
}

function analyzeSituation(info: string): string {
  if (info.includes('clickfunnels') || info.includes('landing page')) {
    return 'Currently using a basic landing page solution';
  }
  if (info.includes('no website') || info.includes('need website')) {
    return 'No current website presence';
  }
  if (info.includes('outdated') || info.includes('old website')) {
    return 'Has an outdated website that needs modernization';
  }
  return 'Looking to improve online presence';
}

function extractTargetMarkets(info: string): string[] {
  const markets: string[] = [];
  
  if (info.includes('homeowner') || info.includes('residential')) {
    markets.push('homeowners');
  }
  if (info.includes('builder') || info.includes('contractor') || info.includes('commercial')) {
    markets.push('builders and contractors');
  }
  if (info.includes('business') && info.includes('client')) {
    markets.push('business clients');
  }
  
  return markets.length > 0 ? markets : ['local customers'];
}

function extractGoals(info: string): string[] {
  const goals: string[] = [];
  
  if (info.includes('professional') || info.includes('credibility') || info.includes('legit')) {
    goals.push('Establish professional credibility and online presence');
  }
  if (info.includes('quote') || info.includes('estimate') || info.includes('lead')) {
    goals.push('Streamline quote requests and lead generation');
  }
  if (info.includes('google') || info.includes('search') || info.includes('seo')) {
    goals.push('Improve search engine visibility and Google presence');
  }
  if (info.includes('showcase') || info.includes('portfolio') || info.includes('work')) {
    goals.push('Showcase quality work and build portfolio');
  }
  
  return goals.length > 0 ? goals : ['Grow business online'];
}

function extractChallenges(info: string): string[] {
  const challenges: string[] = [];
  
  if (info.includes('credibility') || info.includes('trust')) {
    challenges.push('Building credibility with target market');
  }
  if (info.includes('quote') || info.includes('estimate')) {
    challenges.push('Inefficient quote request process');
  }
  if (info.includes('competition') || info.includes('compete')) {
    challenges.push('Standing out from competition');
  }
  
  return challenges;
}

function determinePackage(info: string, targetMarkets: string[], goals: string[], challenges: string[]): {
  package: 'basic' | 'premium';
  confidence: number;
  reasoning: string[];
} {
  let premiumScore = 0;
  const reasoning: string[] = [];
  
  // Multiple target markets favor premium
  if (targetMarkets.length > 1) {
    premiumScore += 0.3;
    reasoning.push('Multiple target markets require dedicated service pages');
  }
  
  // SEO/ranking goals favor premium
  if (info.includes('seo') || info.includes('rank') || info.includes('search')) {
    premiumScore += 0.25;
    reasoning.push('SEO and ranking goals require advanced optimization');
  }
  
  // Professional credibility needs favor premium
  if (info.includes('credibility') || info.includes('professional') || info.includes('builder')) {
    premiumScore += 0.2;
    reasoning.push('Professional credibility requirements need comprehensive features');
  }
  
  // Complex services favor premium
  if (info.includes('service') && (info.includes('multiple') || info.includes('various'))) {
    premiumScore += 0.15;
    reasoning.push('Multiple service offerings benefit from dedicated pages');
  }
  
  // Established business indicators favor premium
  if (info.includes('review') || info.includes('established') || info.includes('experience')) {
    premiumScore += 0.1;
    reasoning.push('Established business can leverage advanced features');
  }
  
  const recommendedPackage = premiumScore >= 0.4 ? 'premium' : 'basic';
  
  if (reasoning.length === 0) {
    reasoning.push(recommendedPackage === 'premium' 
      ? 'Business needs suggest comprehensive solution'
      : 'Basic package provides solid foundation for growth'
    );
  }
  
  return {
    package: recommendedPackage,
    confidence: Math.min(premiumScore + 0.3, 0.95), // Add base confidence
    reasoning
  };
}

function generateClientGreeting(analysis: CustomerAnalysis): string {
  return `Thank you for discussing your vision for ${analysis.businessName}. At GetFound, we specialize in helping ${analysis.businessType} businesses like yours enhance their online presence and attract their ideal customers. ${
    analysis.currentSituation.includes('ClickFunnels') 
      ? `We understand you're looking to move beyond your current landing page to a professional, comprehensive website solution.`
      : `We understand you're looking for a professional online presence that truly represents your business.`
  }`;
}

function generateSituationAnalysis(analysis: CustomerAnalysis): string {
  const markets = analysis.targetMarkets.join(' and ');
  return `We recognize that your business serves ${markets}, and you need a solution that speaks effectively to each audience. ${
    analysis.challenges.length > 0 
      ? `We also understand the challenges you're facing: ${analysis.challenges.join(', ').toLowerCase()}.`
      : ''
  }`;
}

function generateGoalsSection(analysis: CustomerAnalysis): string {
  return `Based on our discussion, we've identified your key objectives:\n\n${
    analysis.keyGoals.map(goal => `• ${goal}`).join('\n')
  }`;
}

function generatePackageReasons(analysis: CustomerAnalysis): string[] {
  if (analysis.recommendedPackage === 'premium') {
    return [
      `Your ${analysis.targetMarkets.join(' and ')} focus requires dedicated service pages for maximum impact`,
      'Advanced SEO features will help you rank for specific services in your area',
      'Professional credibility features will build trust with your target market',
      'Comprehensive lead generation tools will streamline your quote process'
    ];
  } else {
    return [
      'Provides a solid professional foundation for your online presence',
      'GetFound app integration makes content creation effortless',
      'Cost-effective solution that grows with your business',
      'All essential features to establish credibility and generate leads'
    ];
  }
}

function generateBasicFeatures(analysis: CustomerAnalysis): string[] {
  return [
    `Custom 5-page website showcasing ${analysis.businessName}`,
    'GetFound mobile app for effortless content creation',
    'Professional portfolio section with filterable projects',
    'Mobile-responsive design for all devices',
    'Basic SEO setup and optimization',
    'Contact forms and quote request system',
    'Website hosting and maintenance included'
  ];
}

function generatePremiumFeatures(analysis: CustomerAnalysis): string[] {
  const features = [
    'Expanded 10-15+ page website with dedicated service pages',
    'Advanced SEO strategy targeting specific keywords',
    'Smart AI content placement on relevant service pages',
    'Enhanced lead generation system with custom forms',
    'Comprehensive Google Business Profile optimization',
    'Keyword rank tracking dashboard (launching September 2025)',
    'Professional credibility features for B2B clients'
  ];
  
  // Add market-specific features
  if (analysis.targetMarkets.includes('builders')) {
    features.push('Dedicated builder-focused service pages and testimonials');
  }
  if (analysis.targetMarkets.includes('homeowners')) {
    features.push('Homeowner-friendly quote request and measurement systems');
  }
  
  return features;
}

function generateSpecificSolutions(analysis: CustomerAnalysis): string[] {
  const solutions: string[] = [];
  
  if (analysis.businessType.includes('door')) {
    solutions.push('Dedicated "Exterior Door Swaps" page with measurement guide for homeowners');
    solutions.push('Builder-focused pages for interior door and trim installation services');
  }
  
  if (analysis.challenges.some(c => c.includes('quote'))) {
    solutions.push('Streamlined online quote request system reducing unnecessary site visits');
  }
  
  if (analysis.challenges.some(c => c.includes('credibility'))) {
    solutions.push('Professional portfolio showcasing work quality and client testimonials');
  }
  
  return solutions.length > 0 ? solutions : [
    'Custom solutions tailored to your specific business needs',
    'Professional presentation that builds trust with your target market'
  ];
}

function generateNextSteps(analysis: CustomerAnalysis): string[] {
  return [
    'Schedule a follow-up call to discuss this proposal in detail',
    `Select the ${analysis.recommendedPackage} package that aligns with your business goals`,
    'Provide business assets and content for website development',
    'Begin implementation with our specialized team',
    'Launch your new professional online presence'
  ];
}
