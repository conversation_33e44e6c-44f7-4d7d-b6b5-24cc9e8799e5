/**
 * Dynamic Proposal Content Generator
 * Analyzes customer information and generates customized proposal content
 */

export interface CustomerAnalysis {
  businessName: string;
  businessType: string;
  currentSituation: string;
  targetMarkets: string[];
  keyGoals: string[];
  challenges: string[];
  recommendedPackage: 'basic' | 'premium';
  confidence: number;
  reasoning: string[];
}

export interface DynamicProposalContent {
  projectTitle: string;
  clientGreeting: string;
  situationAnalysis: string;
  goalsSection: string;
  packageRecommendation: {
    recommended: 'basic' | 'premium';
    reasoning: string;
    whyThisPackage: string[];
  };
  customizedFeatures: {
    basic: string[];
    premium: string[];
  };
  specificSolutions: string[];
  nextSteps: string[];
}

/**
 * Analyzes customer information and generates dynamic proposal content
 */
export function generateDynamicProposal(customerInfo: string): DynamicProposalContent {
  const analysis = analyzeCustomerInfo(customerInfo);
  
  return {
    projectTitle: `Modern Website & Digital Marketing Solution for ${analysis.businessName}`,
    
    clientGreeting: generateClientGreeting(analysis),
    
    situationAnalysis: generateSituationAnalysis(analysis),
    
    goalsSection: generateGoalsSection(analysis),
    
    packageRecommendation: {
      recommended: analysis.recommendedPackage,
      reasoning: analysis.reasoning.join(' '),
      whyThisPackage: generatePackageReasons(analysis)
    },
    
    customizedFeatures: {
      basic: generateBasicFeatures(analysis),
      premium: generatePremiumFeatures(analysis)
    },
    
    specificSolutions: generateSpecificSolutions(analysis),
    
    nextSteps: generateNextSteps(analysis)
  };
}

/**
 * Analyzes customer information to understand their needs
 */
function analyzeCustomerInfo(customerInfo: string): CustomerAnalysis {
  const info = customerInfo.toLowerCase();
  
  // Extract business details
  const businessName = extractBusinessName(customerInfo);
  const businessType = extractBusinessType(info);
  
  // Analyze current situation
  const currentSituation = analyzeSituation(info);
  
  // Extract target markets
  const targetMarkets = extractTargetMarkets(info);
  
  // Extract goals and challenges
  const keyGoals = extractGoals(info);
  const challenges = extractChallenges(info);
  
  // Determine package recommendation
  const packageAnalysis = determinePackage(info, targetMarkets, keyGoals, challenges);
  
  return {
    businessName,
    businessType,
    currentSituation,
    targetMarkets,
    keyGoals,
    challenges,
    recommendedPackage: packageAnalysis.package,
    confidence: packageAnalysis.confidence,
    reasoning: packageAnalysis.reasoning
  };
}

function extractBusinessName(info: string): string {
  // Look for business name patterns - improved for real emails
  const patterns = [
    /(?:website|listing|business).*?([A-Z][a-z]+[A-Z][a-z]+)\.com/i, // From URLs like loweryfinishing.com
    /([A-Z][a-z]+\s+[A-Z][a-z]+)(?:\s+(?:LLC|Inc|Corp))?/,
    /(?:business|company|firm):\s*([^,\n]+)/i,
    /for\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/i
  ];

  for (const pattern of patterns) {
    const match = info.match(pattern);
    if (match) {
      let name = match[1].trim();
      // Clean up common issues
      if (name.toLowerCase().includes('finishing')) {
        return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
      }
      return name;
    }
  }

  // Fallback: look for any capitalized words that might be business names
  const words = info.split(/\s+/);
  for (let i = 0; i < words.length - 1; i++) {
    if (words[i].match(/^[A-Z][a-z]+$/) && words[i + 1].match(/^[A-Z][a-z]+$/)) {
      return `${words[i]} ${words[i + 1]}`;
    }
  }

  return 'Your Business';
}

function extractBusinessType(info: string): string {
  const lowerInfo = info.toLowerCase();

  // More specific patterns for door/trim contractors
  if (lowerInfo.includes('door') && (lowerInfo.includes('trim') || lowerInfo.includes('finishing'))) {
    return 'door and trim installation contractor';
  }

  const types = {
    'door': 'door installation',
    'trim': 'trim and millwork',
    'finishing': 'finishing contractor',
    'roofing': 'roofing contractor',
    'plumbing': 'plumbing services',
    'electrical': 'electrical services',
    'hvac': 'HVAC services',
    'landscaping': 'landscaping services',
    'cleaning': 'cleaning services',
    'law': 'legal services',
    'medical': 'medical practice',
    'dental': 'dental practice',
    'restaurant': 'restaurant',
    'bakery': 'bakery',
    'retail': 'retail business'
  };

  for (const [keyword, type] of Object.entries(types)) {
    if (lowerInfo.includes(keyword)) return type;
  }

  return 'local business';
}

function analyzeSituation(info: string): string {
  const lowerInfo = info.toLowerCase();

  if (lowerInfo.includes('click funnel') || lowerInfo.includes('clickfunnels')) {
    return 'Currently using ClickFunnels but wants a legitimate, professional website';
  }
  if (lowerInfo.includes('no website') || lowerInfo.includes('need website')) {
    return 'No current website presence';
  }
  if (lowerInfo.includes('outdated') || lowerInfo.includes('old website')) {
    return 'Has an outdated website that needs modernization';
  }
  if (lowerInfo.includes('legit website') || lowerInfo.includes('legitimate website')) {
    return 'Wants to upgrade to a professional, credible website';
  }
  return 'Looking to improve online presence';
}

function extractTargetMarkets(info: string): string[] {
  const markets: string[] = [];
  const lowerInfo = info.toLowerCase();

  if (lowerInfo.includes('homeowner') || lowerInfo.includes('residential')) {
    markets.push('homeowners');
  }
  if (lowerInfo.includes('builder') || lowerInfo.includes('contractor') || lowerInfo.includes('commercial')) {
    markets.push('builders and contractors');
  }
  if (lowerInfo.includes('apartment') || lowerInfo.includes('custom home') || lowerInfo.includes('track home')) {
    if (!markets.includes('builders and contractors')) {
      markets.push('builders and contractors');
    }
  }
  if (lowerInfo.includes('business') && lowerInfo.includes('client')) {
    markets.push('business clients');
  }

  return markets.length > 0 ? markets : ['local customers'];
}

function extractGoals(info: string): string[] {
  const goals: string[] = [];
  const lowerInfo = info.toLowerCase();

  if (lowerInfo.includes('legit website') || lowerInfo.includes('professional') || lowerInfo.includes('credibility')) {
    goals.push('Establish a professional, legitimate website presence');
  }
  if (lowerInfo.includes('easier for customers to request quotes') || lowerInfo.includes('quote')) {
    goals.push('Make it easier for customers to request quotes');
  }
  if (lowerInfo.includes('find the work that i want') || lowerInfo.includes('not be so broad')) {
    goals.push('Target specific types of work and avoid broad, unwanted leads');
  }
  if (lowerInfo.includes('exterior door swaps') && lowerInfo.includes('highlight')) {
    goals.push('Highlight exterior door swaps as a key service offering');
  }
  if (lowerInfo.includes('charge a fee to bid') || lowerInfo.includes('measurement')) {
    goals.push('Implement a system for remote estimates and measurement guidance');
  }
  if (lowerInfo.includes('credibility with big builders') || lowerInfo.includes('show credibility')) {
    goals.push('Build credibility and trust with builders and contractors');
  }
  if (lowerInfo.includes('google') || lowerInfo.includes('search') || lowerInfo.includes('seo')) {
    goals.push('Improve search engine visibility and Google presence');
  }

  return goals.length > 0 ? goals : ['Grow business online'];
}

function extractChallenges(info: string): string[] {
  const challenges: string[] = [];
  const lowerInfo = info.toLowerCase();

  if (lowerInfo.includes('credibility with big builders') || lowerInfo.includes('show credibility')) {
    challenges.push('Building credibility and trust with builders');
  }
  if (lowerInfo.includes('easier for customers to request quotes') || lowerInfo.includes('quote')) {
    challenges.push('Current quote request process needs improvement');
  }
  if (lowerInfo.includes('not be so broad') || lowerInfo.includes('find the work that i want')) {
    challenges.push('Website attracts wrong type of work - too broad');
  }
  if (lowerInfo.includes('automatically shows up') && lowerInfo.includes('annoying')) {
    challenges.push('Google listing has unwanted automatic photo display');
  }
  if (lowerInfo.includes('come out to take measurements') || lowerInfo.includes('driving to the job')) {
    challenges.push('Inefficient measurement and estimation process');
  }

  return challenges;
}

function determinePackage(info: string, targetMarkets: string[], goals: string[], challenges: string[]): {
  package: 'basic' | 'premium';
  confidence: number;
  reasoning: string[];
} {
  let premiumScore = 0;
  const reasoning: string[] = [];
  const lowerInfo = info.toLowerCase();

  // Multiple target markets strongly favor premium
  if (targetMarkets.length > 1) {
    premiumScore += 0.35;
    reasoning.push('Dual market strategy (homeowners + builders) requires dedicated service pages for each audience');
  }

  // Builder credibility needs strongly favor premium
  if (challenges.some((c: string) => c.includes('credibility with builders')) ||
      goals.some((g: string) => g.includes('credibility with builders'))) {
    premiumScore += 0.3;
    reasoning.push('Building credibility with builders requires professional features and dedicated B2B pages');
  }

  // Specific service targeting favors premium
  if (goals.some((g: string) => g.includes('specific types of work')) ||
      challenges.some((c: string) => c.includes('too broad'))) {
    premiumScore += 0.25;
    reasoning.push('Need for targeted service pages to attract specific work types (exterior door swaps, builder services)');
  }

  // Complex quote/measurement system favors premium
  if (goals.some((g: string) => g.includes('remote estimates')) ||
      lowerInfo.includes('measurement') || lowerInfo.includes('fee to bid')) {
    premiumScore += 0.2;
    reasoning.push('Advanced lead generation system needed for remote estimates and measurement guidance');
  }

  // Extensive service list favors premium
  const serviceCount = (lowerInfo.match(/exterior doors|interior doors|pocket doors|bi fold|barn|trim|base|handrail|built in/g) || []).length;
  if (serviceCount >= 5) {
    premiumScore += 0.15;
    reasoning.push('Extensive service offerings benefit from individual dedicated pages for SEO');
  }

  // Established business with reviews
  if (lowerInfo.includes('review') || lowerInfo.includes('good reviews')) {
    premiumScore += 0.1;
    reasoning.push('Established business with reviews can leverage advanced SEO and credibility features');
  }

  const recommendedPackage = premiumScore >= 0.5 ? 'premium' : 'basic';

  if (reasoning.length === 0) {
    reasoning.push(recommendedPackage === 'premium'
      ? 'Business complexity and goals indicate need for comprehensive solution'
      : 'Basic package provides solid foundation for growth'
    );
  }

  return {
    package: recommendedPackage,
    confidence: Math.min(premiumScore + 0.2, 0.95), // Add base confidence
    reasoning
  };
}

function generateClientGreeting(analysis: CustomerAnalysis): string {
  const businessFocus = analysis.businessType.includes('door') ?
    'door and trim installation' : analysis.businessType;

  return `Thank you for discussing your vision for ${analysis.businessName}. At GetFound, we specialize in helping ${businessFocus} businesses like yours enhance their online presence and attract their ideal customers. ${
    analysis.currentSituation.includes('ClickFunnels')
      ? `We understand you're looking to move beyond your current ClickFunnels page to a legitimate, professional website that makes it easier for customers to request quotes and helps you find the specific type of work you want.`
      : `We understand you're looking for a professional online presence that truly represents your business.`
  }`;
}

function generateSituationAnalysis(analysis: CustomerAnalysis): string {
  const markets = analysis.targetMarkets.join(' and ');

  let situationText = `We recognize that your business serves ${markets}, and you need a solution that speaks effectively to each audience.`;

  if (analysis.challenges.length > 0) {
    situationText += ` We also understand the key challenges you're facing: ${analysis.challenges.join('; ')}.`;
  }

  // Add specific insights for door/trim contractors
  if (analysis.businessType.includes('door')) {
    situationText += ` Your focus on exterior door swaps for homeowners and comprehensive installation services for builders requires a sophisticated approach that highlights your expertise in each area.`;
  }

  return situationText;
}

function generateGoalsSection(analysis: CustomerAnalysis): string {
  return `Based on our discussion, we've identified your key objectives:\n\n${
    analysis.keyGoals.map(goal => `• ${goal}`).join('\n')
  }`;
}

function generatePackageReasons(analysis: CustomerAnalysis): string[] {
  if (analysis.recommendedPackage === 'premium') {
    return [
      `Your ${analysis.targetMarkets.join(' and ')} focus requires dedicated service pages for maximum impact`,
      'Advanced SEO features will help you rank for specific services in your area',
      'Professional credibility features will build trust with your target market',
      'Comprehensive lead generation tools will streamline your quote process'
    ];
  } else {
    return [
      'Provides a solid professional foundation for your online presence',
      'GetFound app integration makes content creation effortless',
      'Cost-effective solution that grows with your business',
      'All essential features to establish credibility and generate leads'
    ];
  }
}

function generateBasicFeatures(analysis: CustomerAnalysis): string[] {
  return [
    `Custom 5-page website showcasing ${analysis.businessName}`,
    'GetFound mobile app for effortless content creation',
    'Professional portfolio section with filterable projects',
    'Mobile-responsive design for all devices',
    'Basic SEO setup and optimization',
    'Contact forms and quote request system',
    'Website hosting and maintenance included'
  ];
}

function generatePremiumFeatures(analysis: CustomerAnalysis): string[] {
  const features = [
    'Expanded 10-15+ page website with dedicated service pages',
    'Advanced SEO strategy targeting specific keywords',
    'Smart AI content placement on relevant service pages',
    'Enhanced lead generation system with custom forms',
    'Comprehensive Google Business Profile optimization',
    'Keyword rank tracking dashboard (launching September 2025)',
    'Professional credibility features for B2B clients'
  ];
  
  // Add market-specific features
  if (analysis.targetMarkets.includes('builders')) {
    features.push('Dedicated builder-focused service pages and testimonials');
  }
  if (analysis.targetMarkets.includes('homeowners')) {
    features.push('Homeowner-friendly quote request and measurement systems');
  }
  
  return features;
}

function generateSpecificSolutions(analysis: CustomerAnalysis): string[] {
  const solutions: string[] = [];

  // Door/trim specific solutions
  if (analysis.businessType.includes('door')) {
    solutions.push('Dedicated "Exterior Door Swaps" landing page with measurement guide and fee structure for homeowners');
    solutions.push('Separate builder-focused service pages for interior doors, trim work, and custom installations');
    solutions.push('Professional portfolio showcasing both residential and commercial projects');
  }

  // Quote/measurement system solutions
  if (analysis.keyGoals.some((g: string) => g.includes('remote estimates')) ||
      analysis.challenges.some((c: string) => c.includes('measurement'))) {
    solutions.push('Remote estimation system with measurement instructions to reduce unnecessary site visits');
    solutions.push('Clear fee structure for on-site measurements when remote estimates aren\'t sufficient');
  }

  // Builder credibility solutions
  if (analysis.challenges.some(c => c.includes('credibility with builders'))) {
    solutions.push('Professional B2B presentation with case studies and builder testimonials');
    solutions.push('Dedicated pages for each service type (apartments, custom homes, track homes)');
  }

  // Targeting specific work solutions
  if (analysis.challenges.some(c => c.includes('too broad'))) {
    solutions.push('Service-specific landing pages to attract the exact type of work you want');
    solutions.push('Clear messaging that filters out unwanted leads while attracting ideal customers');
  }

  // Google listing solutions
  if (analysis.challenges.some(c => c.includes('Google listing'))) {
    solutions.push('Google Business Profile optimization to control photo display and improve credibility');
  }

  return solutions.length > 0 ? solutions : [
    'Custom solutions tailored to your specific business needs',
    'Professional presentation that builds trust with your target market'
  ];
}

function generateNextSteps(analysis: CustomerAnalysis): string[] {
  return [
    'Schedule a follow-up call to discuss this proposal in detail',
    `Select the ${analysis.recommendedPackage} package that aligns with your business goals`,
    'Provide business assets and content for website development',
    'Begin implementation with our specialized team',
    'Launch your new professional online presence'
  ];
}
