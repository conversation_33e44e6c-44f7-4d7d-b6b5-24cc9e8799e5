import OpenAI from 'openai';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Configuration
const AI_CONFIG = {
  model: 'gpt-4-turbo-preview', // Use GPT-4 Turbo for best results
  maxTokens: 1500,
  temperature: 0.7, // Balanced creativity and consistency
};

/**
 * Generate AI response for proposal assistance
 */
export async function generateAIResponse(
  prompt: string,
  proposalData: EnhancedProposal,
  section?: string,
  chatHistory: any[] = []
): Promise<string> {
  try {
    // Build context-aware system prompt
    const systemPrompt = buildSystemPrompt(proposalData, section);
    
    // Build conversation history for context
    const messages = buildMessageHistory(systemPrompt, prompt, chatHistory);
    
    console.log('Sending request to OpenAI with model:', AI_CONFIG.model);

    // Create timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Request timed out after 30 seconds')), 30000);
    });

    // Race between API call and timeout
    const completion = await Promise.race([
      openai.chat.completions.create({
        model: AI_CONFIG.model,
        messages,
        max_tokens: AI_CONFIG.maxTokens,
        temperature: AI_CONFIG.temperature,
      }),
      timeoutPromise
    ]) as any;

    const response = completion.choices[0]?.message?.content;
    
    if (!response) {
      throw new Error('No response generated from OpenAI');
    }

    console.log('OpenAI response generated successfully');
    return response.trim();
    
  } catch (error: any) {
    console.error('OpenAI API Error:', error);
    
    // Handle specific error types
    if (error.message?.includes('timed out')) {
      throw new Error('Request timed out. Please try again.');
    } else if (error.code === 'insufficient_quota') {
      throw new Error('OpenAI API quota exceeded. Please check your billing.');
    } else if (error.code === 'invalid_api_key') {
      throw new Error('Invalid OpenAI API key. Please check your configuration.');
    } else if (error.code === 'rate_limit_exceeded') {
      throw new Error('Rate limit exceeded. Please try again in a moment.');
    }
    
    // Generic error fallback
    throw new Error(`AI service error: ${error.message || 'Unknown error'}`);
  }
}

/**
 * Build system prompt based on proposal context and section
 */
function buildSystemPrompt(proposalData: EnhancedProposal, section?: string): string {
  const businessType = proposalData.businessType || 'local business';
  const businessName = proposalData.businessName || 'the client';
  
  let systemPrompt = `You are an expert proposal writer for GetFound, a company that helps local businesses improve their online presence through professional websites and digital marketing services.

CONTEXT:
- Client: ${businessName}
- Business Type: ${businessType}
- Your role: Help create compelling, personalized proposal content

GETFOUND SERVICES:
- Custom professional websites (5+ pages)
- GetFound mobile app integration for portfolio management
- SEO optimization and local search visibility
- Google Business Profile optimization
- Lead generation and quote request systems
- Website hosting and maintenance

PACKAGES OFFERED:
1. Basic "GetFound" Package ($499 setup, $100/month)
   - Custom 5-page website, mobile app integration, basic SEO, hosting
2. Premium "GetFound Plus" Package ($999 setup, $200/month)
   - Everything in Basic plus advanced SEO, social media integration, enhanced features

WRITING STYLE:
- Professional but conversational
- Focus on business benefits and ROI
- Use specific examples relevant to the business type
- Avoid technical jargon
- Keep responses concise but compelling (2-4 paragraphs max)`;

  // Add section-specific guidance
  if (section) {
    systemPrompt += `\n\nSPECIFIC TASK: You are helping with the "${section}" section of the proposal.`;
    
    switch (section) {
      case 'overview':
        systemPrompt += ` Write a compelling project overview that explains GetFound's approach and value proposition specifically for this ${businessType} business. Focus on how our services address their unique challenges.`;
        break;
      case 'clientGoals':
        systemPrompt += ` Identify 4-5 key business goals/challenges that a ${businessType} business typically faces. Format as bullet points with titles and brief explanations.`;
        break;
      case 'packages':
        systemPrompt += ` Suggest improvements to package descriptions, making features more specific to ${businessType} businesses and highlighting clear differentiation between Basic and Premium packages.`;
        break;
      case 'specificQuestions':
        systemPrompt += ` Address 2-3 common questions that ${businessType} businesses have about our services. Format as Q&A pairs.`;
        break;
      case 'nextSteps':
        systemPrompt += ` Create 4-5 clear, actionable next steps for the client to move forward with the proposal.`;
        break;
      case 'addons':
        systemPrompt += ` Suggest 1-2 relevant add-on services for ${businessType} businesses with pricing and descriptions.`;
        break;
    }
  }

  return systemPrompt;
}

/**
 * Build message history for OpenAI conversation
 */
function buildMessageHistory(
  systemPrompt: string,
  currentPrompt: string,
  chatHistory: any[]
): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
  const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
    { role: 'system', content: systemPrompt }
  ];

  // Add recent chat history for context (limit to last 10 messages to avoid token limits)
  const recentHistory = chatHistory.slice(-10);
  
  for (const message of recentHistory) {
    if (message.role === 'user' || message.role === 'assistant') {
      messages.push({
        role: message.role,
        content: message.content
      });
    }
  }

  // Add current user message
  messages.push({ role: 'user', content: currentPrompt });

  return messages;
}

/**
 * Test OpenAI connection
 */
export async function testOpenAIConnection(): Promise<boolean> {
  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo', // Use cheaper model for testing
      messages: [{ role: 'user', content: 'Hello, this is a connection test.' }],
      max_tokens: 10,
    });

    return !!completion.choices[0]?.message?.content;
  } catch (error) {
    console.error('OpenAI connection test failed:', error);
    return false;
  }
}
