/**
 * Enhanced AI chat service for dynamic proposal updates
 * Handles intelligent proposal customization through chat interactions
 */

import { EnhancedProposal } from '@/types/enhanced-proposal-schema';
import { CustomerProfile, PackageRecommendation, analyzeCustomerProfile, generateAICustomizationPrompt } from './ai-proposal-analyzer';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  proposalUpdates?: ProposalUpdate[];
}

export interface ProposalUpdate {
  section: 'overview' | 'clientGoals' | 'packages' | 'nextSteps' | 'timeline';
  action: 'update' | 'add' | 'remove';
  content: any;
  reasoning?: string;
}

export interface ChatContext {
  customerProfile?: CustomerProfile;
  packageRecommendation?: PackageRecommendation;
  currentProposal: EnhancedProposal;
  chatHistory: ChatMessage[];
}

/**
 * Processes user message and generates AI response with proposal updates
 */
export async function processProposalChatMessage(
  message: string,
  context: ChatContext
): Promise<{
  response: string;
  proposalUpdates?: ProposalUpdate[];
  updatedProposal?: Partial<EnhancedProposal>;
}> {
  try {
    // Analyze the message for proposal update intent
    const updateIntent = analyzeUpdateIntent(message);
    
    // Generate AI response based on context and intent
    const aiResponse = await generateAIResponse(message, context, updateIntent);
    
    // Extract proposal updates from AI response
    const proposalUpdates = extractProposalUpdates(aiResponse, context);
    
    // Apply updates to create updated proposal
    const updatedProposal = applyProposalUpdates(context.currentProposal, proposalUpdates);
    
    return {
      response: aiResponse.content,
      proposalUpdates,
      updatedProposal
    };
  } catch (error) {
    console.error('Error processing chat message:', error);
    return {
      response: "I apologize, but I encountered an error processing your request. Please try again or rephrase your message."
    };
  }
}

/**
 * Analyzes user message to determine proposal update intent
 */
function analyzeUpdateIntent(message: string): {
  hasUpdateIntent: boolean;
  sections: string[];
  action: 'update' | 'analyze' | 'recommend';
} {
  const lowerMessage = message.toLowerCase();
  
  // Keywords that indicate update intent
  const updateKeywords = ['update', 'change', 'modify', 'edit', 'revise', 'customize'];
  const sectionKeywords = {
    overview: ['overview', 'description', 'introduction', 'summary'],
    clientGoals: ['goals', 'objectives', 'targets', 'aims'],
    packages: ['package', 'pricing', 'features', 'services'],
    nextSteps: ['next steps', 'timeline', 'process', 'implementation']
  };
  
  const hasUpdateIntent = updateKeywords.some(keyword => lowerMessage.includes(keyword));
  
  const sections: string[] = [];
  Object.entries(sectionKeywords).forEach(([section, keywords]) => {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      sections.push(section);
    }
  });
  
  // Determine action type
  let action: 'update' | 'analyze' | 'recommend' = 'analyze';
  if (hasUpdateIntent) action = 'update';
  else if (lowerMessage.includes('recommend') || lowerMessage.includes('suggest')) action = 'recommend';
  
  return { hasUpdateIntent, sections, action };
}

/**
 * Generates AI response using OpenAI/Claude API
 */
async function generateAIResponse(
  message: string,
  context: ChatContext,
  intent: any
): Promise<{ content: string; updates?: any[] }> {
  // For now, return a simulated response
  // In production, this would call the actual AI API
  
  const { customerProfile, packageRecommendation, currentProposal } = context;
  
  // Generate contextual response based on intent
  if (intent.action === 'recommend' && customerProfile) {
    const recommendation = packageRecommendation || analyzeCustomerProfile(customerProfile);
    
    return {
      content: `Based on my analysis of ${customerProfile.businessName}, I recommend the **${recommendation.recommendedPackage.toUpperCase()} package** with ${Math.round(recommendation.confidence * 100)}% confidence.

**Key Reasoning:**
${recommendation.reasoning.map(r => `• ${r}`).join('\n')}

**Recommended Customizations:**
• **Overview**: ${recommendation.customizations.overview || 'Customize based on business focus'}
• **Client Goals**: ${recommendation.customizations.clientGoals?.join(', ') || 'Align with business objectives'}
• **Next Steps**: Tailored implementation plan for your business model

Would you like me to update the proposal with these customizations?`,
      updates: [{
        section: 'packages',
        action: 'update',
        content: { recommended: recommendation.recommendedPackage },
        reasoning: recommendation.reasoning.join('; ')
      }]
    };
  }
  
  if (intent.hasUpdateIntent && intent.sections.length > 0) {
    const section = intent.sections[0];
    
    switch (section) {
      case 'overview':
        return {
          content: `I'll help you update the proposal overview. Based on the customer profile, here's a customized overview:

**Updated Overview:**
"Our team at GetFound is excited to help transform ${customerProfile?.businessName || 'your business'}'s online presence. As specialists working with ${customerProfile?.businessType || 'local businesses'}, we understand the unique challenges you face in attracting and converting the right customers."

${customerProfile?.targetMarkets && customerProfile.targetMarkets.length > 1 ? 
  `With your focus on ${customerProfile.targetMarkets.join(' and ')}, you need a sophisticated strategy that speaks to each market segment effectively.` : ''}

Would you like me to apply this update to the proposal?`,
          updates: [{
            section: 'overview',
            action: 'update',
            content: {
              description: `Our team at GetFound is excited to help transform ${customerProfile?.businessName || 'your business'}'s online presence. As specialists working with ${customerProfile?.businessType || 'local businesses'}, we understand the unique challenges you face in attracting and converting the right customers.`
            }
          }]
        };
        
      case 'clientGoals':
        const customGoals = generateCustomClientGoals(customerProfile);
        return {
          content: `I'll update the client goals based on the customer's specific needs:

**Updated Client Goals:**
${customGoals.map(goal => `• ${goal}`).join('\n')}

These goals are tailored to address their specific challenges and target markets. Should I apply these updates?`,
          updates: [{
            section: 'clientGoals',
            action: 'update',
            content: { primary: customGoals }
          }]
        };
        
      default:
        return {
          content: `I can help you update the ${section} section. What specific changes would you like me to make?`
        };
    }
  }
  
  // General conversational response
  return {
    content: `I'm here to help customize this proposal for ${customerProfile?.businessName || 'your client'}. I can:

• **Analyze** their business needs and recommend the best package
• **Update** proposal sections like overview, goals, and next steps
• **Customize** content based on their industry and target markets
• **Suggest** specific features and benefits relevant to their business

What would you like me to help you with?`
  };
}

/**
 * Generates custom client goals based on customer profile
 */
function generateCustomClientGoals(profile?: CustomerProfile): string[] {
  if (!profile) return [];
  
  const goals: string[] = [];
  
  // Add goals based on challenges
  if (profile.challenges?.toLowerCase().includes('credibility')) {
    goals.push("Establish professional credibility and trust with potential clients");
  }
  
  if (profile.challenges?.toLowerCase().includes('quote')) {
    goals.push("Streamline the quote request process for better lead conversion");
  }
  
  // Add goals based on target markets
  if (profile.targetMarkets?.some(market => market.toLowerCase().includes('builder'))) {
    goals.push("Build trust and credibility with builders and contractors");
    goals.push("Showcase expertise and reliability for B2B partnerships");
  }
  
  if (profile.targetMarkets?.some(market => market.toLowerCase().includes('homeowner'))) {
    goals.push("Attract and convert homeowners seeking quality services");
    goals.push("Create an easy-to-use quote request system for residential customers");
  }
  
  // Add general goals based on business type
  if (profile.businessType?.toLowerCase().includes('installation')) {
    goals.push("Showcase quality workmanship through professional portfolio");
    goals.push("Improve local search visibility for service area coverage");
  }
  
  return goals.length > 0 ? goals : [
    "Establish a strong online presence",
    "Generate qualified leads",
    "Build customer trust and credibility"
  ];
}

/**
 * Extracts proposal updates from AI response
 */
function extractProposalUpdates(aiResponse: any, context: ChatContext): ProposalUpdate[] {
  return aiResponse.updates || [];
}

/**
 * Applies proposal updates to create updated proposal
 */
function applyProposalUpdates(
  currentProposal: EnhancedProposal,
  updates: ProposalUpdate[]
): Partial<EnhancedProposal> {
  const updatedProposal: Partial<EnhancedProposal> = { ...currentProposal };
  
  updates.forEach(update => {
    switch (update.section) {
      case 'overview':
        if (update.action === 'update') {
          updatedProposal.overview = {
            ...updatedProposal.overview,
            ...update.content
          };
        }
        break;
        
      case 'clientGoals':
        if (update.action === 'update') {
          updatedProposal.clientGoals = {
            ...updatedProposal.clientGoals,
            ...update.content
          };
        }
        break;
        
      case 'packages':
        if (update.action === 'update') {
          updatedProposal.packages = updatedProposal.packages?.map(pkg => ({
            ...pkg,
            ...update.content
          })) || [];
        }
        break;
        
      case 'nextSteps':
        if (update.action === 'update') {
          updatedProposal.nextSteps = update.content;
        }
        break;
    }
  });
  
  return updatedProposal;
}
