import React, { useState, useRef, useEffect } from 'react';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';

interface ProposalChatAssistantProps {
  proposalId: string;
  chatHistory: any[];
  onChatHistoryChange: (history: any[]) => void;
  proposalData: EnhancedProposal;
  onProposalUpdate?: (updatedData: any) => void;
}

const ProposalChatAssistant: React.FC<ProposalChatAssistantProps> = ({
  proposalId,
  chatHistory,
  onChatHistoryChange,
  proposalData,
  onProposalUpdate
}) => {
  const [chatMessage, setChatMessage] = useState('');
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [selectedSection, setSelectedSection] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const chatEndRef = useRef<HTMLDivElement>(null);
  
  // Section options for quick selection
  const sectionOptions = [
    { id: 'overview', label: 'Project Overview' },
    { id: 'clientGoals', label: 'Client Goals' },
    { id: 'packages', label: 'Packages & Features' },
    { id: 'specificQuestions', label: 'Specific Questions' },
    { id: 'nextSteps', label: 'Next Steps' },
    { id: 'addons', label: 'Add-on Services' }
  ];
  
  // Scroll to bottom effect for chat
  useEffect(() => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [chatHistory]);
  
  // Handle sending messages to AI assistant
  const handleSendMessage = async () => {
    if (!chatMessage.trim() || isSendingMessage) return;

    setIsSendingMessage(true);
    setError(null);

    const messageToSend = chatMessage; // Store message before clearing

    try {
      // Add the user message to chat history immediately for better UX
      const updatedChatHistory = [
        ...chatHistory,
        {
          role: 'user',
          content: messageToSend,
          timestamp: new Date().toISOString()
        }
      ];

      onChatHistoryChange(updatedChatHistory);
      setChatMessage('');

      // Send the message to the AI assistant
      const response = await fetch('/api/ai-proposal-assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          proposalId: proposalId,
          message: messageToSend,
          section: selectedSection || undefined
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to get AI response');
      }
      
      const data = await response.json();

      // Update chat history with the complete history from the server
      if (data.chatHistory) {
        onChatHistoryChange(data.chatHistory);
      }

      // Handle proposal updates from AI
      if (data.updatedProposalData && onProposalUpdate) {
        console.log('AI updated proposal data:', data.updatedProposalData);
        onProposalUpdate(data.updatedProposalData);
      }

      // Reset selected section after sending
      setSelectedSection('');
    } catch (error: any) {
      console.error('Error sending message to AI:', error);
      // Add error message to chat
      onChatHistoryChange([
        ...chatHistory,
        {
          role: 'system',
          content: 'Sorry, there was an error processing your request. Please try again.',
          timestamp: new Date().toISOString()
        }
      ]);
    } finally {
      setIsSendingMessage(false);
    }
  };
  
  // Format chat timestamp
  const formatChatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      return '';
    }
  };
  
  // Generate section-specific placeholders
  const getPlaceholder = () => {
    if (!selectedSection) return "Ask the AI assistant...";
    
    switch (selectedSection) {
      case 'overview':
        return `Help me write an overview for ${proposalData.businessName}...`;
      case 'clientGoals':
        return `Help identify key goals for ${proposalData.businessName}...`;
      case 'packages':
        return `Help improve package descriptions for ${proposalData.businessType} business...`;
      case 'specificQuestions':
        return `Help address questions a ${proposalData.businessType} might have...`;
      case 'nextSteps':
        return `Suggest next steps for this proposal...`;
      default:
        return `Help me with the ${selectedSection} section...`;
    }
  };
  
  return (
    <div className="flex flex-col bg-white shadow rounded-lg" style={{ minHeight: '800px' }}>
      <div className="bg-blue-50 p-4 border-b border-blue-100">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
          AI Proposal Assistant
        </h3>
        <p className="text-sm text-gray-600">
          Ask questions or get help with specific sections
        </p>
      </div>
      
      <div className="flex-grow overflow-y-auto p-4 bg-gray-50 space-y-4">
        {chatHistory.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <svg className="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
            </svg>
            <p className="mt-2">
              No messages yet. Ask the AI assistant for help with your proposal.
            </p>
          </div>
        ) : (
          chatHistory.map((message, index) => (
            <div 
              key={index} 
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div 
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === 'user' 
                    ? 'bg-primary-100 text-primary-800'
                    : message.role === 'system'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-white border border-gray-200'
                }`}
              >
                <div className="text-sm whitespace-pre-line">{message.content}</div>
                {message.timestamp && (
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {formatChatTime(message.timestamp)}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={chatEndRef} />
      </div>
      
      {/* Section selector */}
      <div className="p-2 border-t border-gray-200 bg-gray-50 overflow-x-auto">
        <div className="flex space-x-2">
          {sectionOptions.map(option => (
            <button
              key={option.id}
              onClick={() => setSelectedSection(selectedSection === option.id ? '' : option.id)}
              className={`px-3 py-1 text-xs rounded-full whitespace-nowrap ${
                selectedSection === option.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
      
      <div className="p-4 border-t border-gray-200 bg-white">
        <div className="flex items-center">
          <input
            type="text"
            value={chatMessage}
            onChange={(e) => setChatMessage(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder={getPlaceholder()}
            className="flex-grow px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            disabled={isSendingMessage}
          />
          <button
            type="button"
            onClick={handleSendMessage}
            disabled={isSendingMessage || !chatMessage.trim()}
            className={`px-4 py-2 rounded-r-md focus:outline-none ${
              isSendingMessage || !chatMessage.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            {isSendingMessage ? (
              <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            )}
          </button>
        </div>
        
        {selectedSection && (
          <div className="mt-2 text-xs bg-blue-50 p-2 rounded-md text-blue-700">
            <span className="font-medium">Editing:</span> {sectionOptions.find(opt => opt.id === selectedSection)?.label}
            <button 
              onClick={() => setSelectedSection('')}
              className="ml-2 text-blue-500 hover:text-blue-700"
            >
              <svg className="w-3 h-3 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        )}
        
        <p className="mt-2 text-xs text-gray-500">
          {selectedSection 
            ? `The AI will help improve the ${sectionOptions.find(opt => opt.id === selectedSection)?.label.toLowerCase()} section of your proposal.`
            : 'Ask about packages, features, benefits, timeline, or other aspects of the proposal.'}
        </p>
      </div>
    </div>
  );
};

export default ProposalChatAssistant; 