'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';

export default function AuthTestPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState('');
  const [session, setSession] = useState<any>(null);
  const [user, setUser] = useState<any>(null);
  
  const supabase = createClientComponentClient();
  const router = useRouter();

  useEffect(() => {
    checkSession();
  }, []);

  const checkSession = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        setStatus(`Session error: ${error.message}`);
        return;
      }
      
      if (session) {
        setSession(session);
        setUser(session.user);
        setStatus(`✅ Logged in as: ${session.user.email}`);
      } else {
        setStatus('❌ No active session');
        setSession(null);
        setUser(null);
      }
    } catch (error: any) {
      setStatus(`Error checking session: ${error.message}`);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setStatus('Logging in...');

    try {
      // Clear any existing session first
      await supabase.auth.signOut();
      
      // Sign in with email and password
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      if (!data.session) {
        throw new Error('No session created');
      }

      setSession(data.session);
      setUser(data.user);
      setStatus(`✅ Login successful! Session created for ${data.user.email}`);
      
      // Force a page refresh to ensure cookies are set
      setTimeout(() => {
        window.location.href = '/admin/proposals';
      }, 1000);
      
    } catch (error: any) {
      console.error('Login error:', error);
      setStatus(`❌ Login failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      setSession(null);
      setUser(null);
      setStatus('✅ Logged out successfully');
    } catch (error: any) {
      setStatus(`❌ Logout error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testProposalsAPI = async () => {
    try {
      setStatus('Testing proposals API...');
      
      const response = await fetch('/api/list-proposals', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setStatus(`✅ API Success: Found ${data.proposals?.length || 0} proposals`);
      } else {
        setStatus(`❌ API Error: ${data.error} - ${data.message}`);
      }
    } catch (error: any) {
      setStatus(`❌ API Request failed: ${error.message}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Authentication Test
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Test login and API access
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          
          {/* Status Display */}
          <div className="mb-6 p-4 bg-gray-100 rounded-md">
            <h3 className="font-medium text-gray-900 mb-2">Status:</h3>
            <p className="text-sm text-gray-700">{status}</p>
          </div>

          {/* Session Info */}
          {session && (
            <div className="mb-6 p-4 bg-green-50 rounded-md">
              <h3 className="font-medium text-green-900 mb-2">Session Info:</h3>
              <p className="text-sm text-green-700">User ID: {user?.id}</p>
              <p className="text-sm text-green-700">Email: {user?.email}</p>
              <p className="text-sm text-green-700">Expires: {new Date(session.expires_at * 1000).toLocaleString()}</p>
            </div>
          )}

          {/* Login Form */}
          {!session && (
            <form onSubmit={handleLogin} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {loading ? 'Logging in...' : 'Sign In'}
              </button>
            </form>
          )}

          {/* Action Buttons */}
          <div className="mt-6 space-y-3">
            <button
              onClick={checkSession}
              className="w-full py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Check Session
            </button>
            
            {session && (
              <>
                <button
                  onClick={testProposalsAPI}
                  className="w-full py-2 px-4 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100"
                >
                  Test Proposals API
                </button>
                
                <button
                  onClick={handleLogout}
                  disabled={loading}
                  className="w-full py-2 px-4 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100"
                >
                  Sign Out
                </button>
                
                <button
                  onClick={() => router.push('/admin/proposals')}
                  className="w-full py-2 px-4 border border-green-300 rounded-md shadow-sm text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100"
                >
                  Go to Proposals Page
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
