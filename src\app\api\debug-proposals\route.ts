import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';

export async function GET(request: NextRequest) {
  try {
    console.log('=== PROPOSAL DEBUG DIAGNOSTICS ===');
    
    // Create a Supabase client that uses cookies
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const diagnostics: any = {
      timestamp: new Date().toISOString(),
      environment: {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'MISSING',
        supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'MISSING',
      },
      auth: {},
      database: {},
      errors: []
    };
    
    // Test authentication
    try {
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        diagnostics.auth.error = userError.message;
        diagnostics.auth.code = userError.code;
        diagnostics.errors.push(`Auth Error: ${userError.message}`);
      } else if (!userData.user) {
        diagnostics.auth.status = 'NO_USER';
        diagnostics.errors.push('No authenticated user found');
      } else {
        diagnostics.auth.status = 'AUTHENTICATED';
        diagnostics.auth.userId = userData.user.id;
        diagnostics.auth.email = userData.user.email;
      }
    } catch (authError: any) {
      diagnostics.auth.error = authError.message;
      diagnostics.errors.push(`Auth Exception: ${authError.message}`);
    }
    
    // Test database connection and schema
    try {
      // Check if proposals table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('proposals')
        .select('id')
        .limit(1);
        
      if (tableError) {
        diagnostics.database.tableError = tableError.message;
        diagnostics.database.tableCode = tableError.code;
        diagnostics.errors.push(`Table Error: ${tableError.message}`);
      } else {
        diagnostics.database.tableExists = true;
        diagnostics.database.recordCount = tableCheck?.length || 0;
      }
    } catch (dbError: any) {
      diagnostics.database.error = dbError.message;
      diagnostics.errors.push(`Database Exception: ${dbError.message}`);
    }
    
    // Try to fetch proposals (same as the actual API)
    try {
      const { data: proposals, error: proposalsError } = await supabase
        .from('proposals')
        .select('id, client_name, business_name, reference_number, created_at, updated_at')
        .order('created_at', { ascending: false })
        .limit(5);
        
      if (proposalsError) {
        diagnostics.database.proposalsError = proposalsError.message;
        diagnostics.database.proposalsCode = proposalsError.code;
        diagnostics.errors.push(`Proposals Query Error: ${proposalsError.message}`);
      } else {
        diagnostics.database.proposalsCount = proposals?.length || 0;
        diagnostics.database.sampleProposals = proposals?.map(p => ({
          id: p.id,
          client_name: p.client_name,
          business_name: p.business_name,
          created_at: p.created_at
        })) || [];
      }
    } catch (proposalsError: any) {
      diagnostics.database.proposalsException = proposalsError.message;
      diagnostics.errors.push(`Proposals Exception: ${proposalsError.message}`);
    }
    
    // Check RLS policies
    try {
      const { data: rlsCheck, error: rlsError } = await supabase.rpc('check_rls_policies');
      if (rlsError && rlsError.code !== '42883') { // Function doesn't exist is OK
        diagnostics.database.rlsError = rlsError.message;
      }
    } catch (rlsError: any) {
      // RLS check is optional
    }
    
    console.log('Diagnostics completed:', diagnostics);
    
    return NextResponse.json({
      success: diagnostics.errors.length === 0,
      diagnostics,
      summary: {
        authWorking: !diagnostics.auth.error,
        databaseWorking: !diagnostics.database.tableError && !diagnostics.database.proposalsError,
        totalErrors: diagnostics.errors.length
      }
    });
    
  } catch (error: any) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json({
      success: false,
      error: 'Debug endpoint failed',
      message: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
