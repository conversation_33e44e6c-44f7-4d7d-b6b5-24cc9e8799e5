'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { isAdmin } from '@/lib/auth';
import { supabaseWithCookies as supabase } from '@/lib/supabase';
import { EnhancedProposal, createDefaultEnhancedProposal } from '@/types/enhanced-proposal-schema';
import { convertToEnhancedProposal } from '@/lib/ai-proposal-helper';
import ProposalChatAssistant from '@/components/ai/ProposalChatAssistant';
import SplitScreenEditor from '@/components/proposal/SplitScreenEditor';

// Package templates
const PACKAGE_TEMPLATES = {
  basic: {
    name: "Basic \"GetFound\" Package",
    setupFee: "$499",
    monthlyFee: "$100",
    features: [
      "Custom 5-Page Professional Website",
      "GetFound Mobile App Integration",
      "Filterable Service Portfolio",
      "Mobile-Responsive Design",
      "Basic SEO Setup & Optimization",
      "Standard Quote Request Forms",
      "Website Hosting & Maintenance",
      "Google Business Profile Optimization"
    ]
  },
  premium: {
    name: "Premium \"GetFound\" SEO & Growth Package",
    setupFee: "$2,999",
    monthlyFee: "$150",
    features: [
      "Everything in Basic Package",
      "Expanded Website (10-15+ Pages)",
      "Dedicated Service Pages for Each Offering",
      "Advanced SEO Strategy & Keyword Research",
      "Smart AI Content Builder & Placement",
      "Enhanced Lead Generation System",
      "Comprehensive Google Business Profile Management",
      "Local SEO Optimization for Your Service Area",
      "Keyword Rank Tracking Dashboard Access",
      "Ongoing SEO Reports & Performance Tracking"
    ]
  }
};

// Payment option templates
const PAYMENT_TEMPLATES = {
  basic: [
    "Months 1-12: $141.58/month (includes $100 standard monthly + $41.58 for setup fee)",
    "Month 13 onwards: $100/month"
  ],
  premium: [
    "Months 1-12: $399.92/month (includes $150 standard monthly + $249.92 for setup fee)",
    "Month 13 onwards: $150/month"
  ]
};

function ProposalEditorContent() {
  const router = useRouter();
  const params = useParams();
  const proposalId = params.id as string;
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [proposal, setProposal] = useState<any>(null);
  const [industryContent, setIndustryContent] = useState<any>(null);
  const [uiMode, setUiMode] = useState<'split' | 'classic'>('split');
  
  // AI Chat state
  const [showAiChat, setShowAiChat] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [chatHistory, setChatHistory] = useState<any[]>([]);
  const chatEndRef = React.useRef<HTMLDivElement>(null);
  
  // Form data
  const [formData, setFormData] = useState({
    clientName: '',
    businessName: '',
    businessType: '',
    challenges: '',
    notes: '',
    referenceNumber: '',
    status: 'draft'
  });
  
  // Sections data
  const [packages, setPackages] = useState<any[]>([]);
  const [selectedPackages, setSelectedPackages] = useState<string[]>(['basic']);
  const [nextSteps, setNextSteps] = useState<string[]>([
    "Schedule a follow-up call to discuss this proposal in more detail.",
    "Select the package that best fits your business needs and goals.",
    "Sign the agreement and submit the initial payment.",
    "Begin the onboarding process with our team to gather your business information and assets."
  ]);
  
  // Add state for enhanced proposal format
  const [enhancedProposal, setEnhancedProposal] = useState<EnhancedProposal | null>(null);
  
  // Load UI mode preference from localStorage on component mount
  useEffect(() => {
    // Only run on the client
    if (typeof window !== 'undefined') {
      const savedMode = localStorage.getItem('proposalEditorMode');
      if (savedMode && (savedMode === 'split' || savedMode === 'classic')) {
        setUiMode(savedMode as 'split' | 'classic');
      }
    }
  }, []);
  
  // Save UI mode preference to localStorage when it changes
  const toggleUiMode = () => {
    const newMode = uiMode === 'split' ? 'classic' : 'split';
    setUiMode(newMode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('proposalEditorMode', newMode);
    }
  };
  
  // Load proposal data
  useEffect(() => {
    const fetchProposal = async () => {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('proposals')
          .select('*')
          .eq('id', proposalId)
          .single();
        
        if (error) {
          throw error;
        }
        
        setProposal(data);
        
        // Set form data from proposal
        setFormData({
          clientName: data.client_name || '',
          businessName: data.business_name || '',
          businessType: data.business_type || '',
          challenges: data.challenges || '',
          notes: data.notes || '',
          referenceNumber: data.reference_number || '',
          status: data.status || 'draft'
        });
        
        // Initialize chat history if available
        if (data.chat_history && Array.isArray(data.chat_history)) {
          setChatHistory(data.chat_history);
        }
        
        // Try to convert to enhanced proposal format
        try {
          if (data.proposal_data) {
            // Try to use the enhanced schema if it's already there
            if (data.proposal_data.clientName || data.proposal_data.businessName) {
              setEnhancedProposal(data.proposal_data as EnhancedProposal);
            } else {
              // Otherwise convert from the old format
              const converted = convertToEnhancedProposal({
                ...data,
                ...data.proposal_data
              });
              setEnhancedProposal(converted);
              
              // Set packages from the converted format
              if (converted.packages && converted.packages.length > 0) {
                setPackages(converted.packages);
              }
              
              // Set next steps from the converted format
              if (converted.nextSteps && converted.nextSteps.length > 0) {
                setNextSteps(converted.nextSteps);
              }
            }
          } else {
            // Create a default enhanced proposal if no data exists
            const defaultProposal = createDefaultEnhancedProposal(
              data.client_name || '',
              data.business_name || '',
              data.business_type || ''
            );
            setEnhancedProposal(defaultProposal);
            
            // Initialize package data from the default
            setPackages(defaultProposal.packages);
            setNextSteps(defaultProposal.nextSteps);
          }
        } catch (conversionError) {
          console.error('Error converting to enhanced format:', conversionError);
          
          // Initialize package data
          let proposalPackages = [];
          if (data.proposal_data && data.proposal_data.packages) {
            proposalPackages = data.proposal_data.packages;
          } else {
            // Default packages
            proposalPackages = [
              PACKAGE_TEMPLATES.basic,
              PACKAGE_TEMPLATES.premium
            ];
          }
          setPackages(proposalPackages);
          
          // Initialize next steps
          if (data.proposal_data && data.proposal_data.nextSteps) {
            setNextSteps(data.proposal_data.nextSteps);
          }
        }
        
        // Fetch industry-specific content if business type is available
        if (data.business_type) {
          fetchIndustryContent(data.business_type);
        }
      } catch (error: any) {
        console.error('Error fetching proposal:', error);
        setError('Failed to load proposal. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProposal();
  }, [proposalId]);
  
  // Fetch industry-specific content
  const fetchIndustryContent = async (businessType: string) => {
    try {
      const { data, error } = await supabase
        .from('industry_content')
        .select('*')
        .eq('industry', businessType);
      
      if (error) {
        console.error('Error fetching industry content:', error);
        return;
      }
      
      if (data && data.length > 0) {
        // Organize content by type
        const contentByType = data.reduce((acc: any, item: any) => {
          acc[item.content_type] = item.content_data;
          return acc;
        }, {});
        
        setIndustryContent(contentByType);
      }
    } catch (error) {
      console.error('Error fetching industry content:', error);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Update form data - used by split screen editor
  const handleFormDataChange = (newFormData: any) => {
    setFormData(newFormData);
  };
  
  // Update enhanced proposal - used by split screen editor
  const handleUpdateProposal = (updatedProposal: EnhancedProposal) => {
    setEnhancedProposal(updatedProposal);
  };
  
  // Handle package selection
  const handlePackageSelect = (packageType: string) => {
    if (selectedPackages.includes(packageType)) {
      setSelectedPackages(selectedPackages.filter(p => p !== packageType));
    } else {
      setSelectedPackages([...selectedPackages, packageType]);
    }
  };
  
  // Handle package edit
  const handlePackageEdit = (index: number, field: string, value: any) => {
    const updatedPackages = [...packages];
    updatedPackages[index] = {
      ...updatedPackages[index],
      [field]: value
    };
    setPackages(updatedPackages);
  };
  
  // Handle feature edit
  const handleFeatureEdit = (packageIndex: number, featureIndex: number, value: string) => {
    const updatedPackages = [...packages];
    updatedPackages[packageIndex].features[featureIndex] = value;
    setPackages(updatedPackages);
  };
  
  // Handle adding feature
  const handleAddFeature = (packageIndex: number) => {
    const updatedPackages = [...packages];
    if (!updatedPackages[packageIndex].features) {
      updatedPackages[packageIndex].features = [];
    }
    updatedPackages[packageIndex].features.push('');
    setPackages(updatedPackages);
  };
  
  // Handle removing feature
  const handleRemoveFeature = (packageIndex: number, featureIndex: number) => {
    const updatedPackages = [...packages];
    updatedPackages[packageIndex].features.splice(featureIndex, 1);
    setPackages(updatedPackages);
  };
  
  // Handle next step edit
  const handleNextStepEdit = (index: number, value: string) => {
    const updatedNextSteps = [...nextSteps];
    updatedNextSteps[index] = value;
    setNextSteps(updatedNextSteps);
  };
  
  // Handle adding next step
  const handleAddNextStep = () => {
    setNextSteps([...nextSteps, '']);
  };
  
  // Handle removing next step
  const handleRemoveNextStep = (index: number) => {
    const updatedNextSteps = [...nextSteps];
    updatedNextSteps.splice(index, 1);
    setNextSteps(updatedNextSteps);
  };
  
  // Handle submit (saving the proposal)
  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    
    try {
      setSaving(true);
      
      // Prepare enhanced proposal data - include all sections
      let proposalData: EnhancedProposal;
      
      if (enhancedProposal) {
        // Use the enhanced proposal structure with updated data
        proposalData = {
          ...enhancedProposal,
          clientName: formData.clientName,
          businessName: formData.businessName,
          businessType: formData.businessType,
          packages: packages,
          nextSteps: nextSteps
        };
      } else {
        // Create a new enhanced proposal
        proposalData = {
          clientName: formData.clientName,
          businessName: formData.businessName,
          businessType: formData.businessType,
          packages: packages,
          nextSteps: nextSteps
        };
      }
      
      // Save to Supabase
      const { error } = await supabase
        .from('proposals')
        .update({
          client_name: formData.clientName,
          business_name: formData.businessName,
          business_type: formData.businessType,
          challenges: formData.challenges,
          notes: formData.notes,
          reference_number: formData.referenceNumber,
          status: formData.status,
          proposal_data: proposalData,
          chat_history: chatHistory,
          updated_at: new Date().toISOString()
        })
        .eq('id', proposalId);
      
      if (error) {
        throw error;
      }
      
      alert('Proposal saved successfully!');
    } catch (error: any) {
      console.error('Error saving proposal:', error);
      alert(`Failed to save proposal: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };
  
  // Generate shareable link
  const getShareableLink = () => {
    if (!proposal) return '';
    return `${window.location.origin}/view/proposals/${proposal.id}`;
  };
  
  // Copy shareable link to clipboard
  const copyShareableLink = () => {
    const link = getShareableLink();
    navigator.clipboard.writeText(link)
      .then(() => {
        alert('Link copied to clipboard!');
      })
      .catch(err => {
        console.error('Failed to copy link: ', err);
      });
  };
  
  // Handle sending proposal to client
  const handleSendProposal = async () => {
    if (!proposal) return;
    
    try {
      // Update status to 'sent'
      const { error } = await supabase
        .from('proposals')
        .update({
          status: 'sent'
        })
        .eq('id', proposalId);
      
      if (error) {
        throw error;
      }
      
      // Create sent event
      await supabase
        .from('proposal_events')
        .insert({
          proposal_id: proposalId,
          event_type: 'sent',
          event_data: {
            sent_by: 'admin',
            sent_at: new Date().toISOString()
          }
        });
      
      // Update local state
      setFormData(prev => ({ ...prev, status: 'sent' }));
      
      // Show success message
      alert('Proposal marked as sent!');
    } catch (error: any) {
      console.error('Error sending proposal:', error);
      alert('Failed to send proposal: ' + error.message);
    }
  };
  
  // Add scroll to bottom effect for chat
  useEffect(() => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [chatHistory]);
  
  // Handle sending messages to AI assistant
  const handleSendMessage = async () => {
    if (!chatMessage.trim() || isSendingMessage) return;
    
    setIsSendingMessage(true);
    
    try {
      // Add the user message to chat history immediately for better UX
      const updatedChatHistory = [
        ...chatHistory,
        {
          role: 'user',
          content: chatMessage,
          timestamp: new Date().toISOString()
        }
      ];
      
      setChatHistory(updatedChatHistory);
      setChatMessage('');
      
      // Send the message to the AI assistant
      const response = await fetch('/api/ai-proposal-assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          proposalId: proposalId,
          message: chatMessage
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to get AI response');
      }
      
      const data = await response.json();
      
      // Update chat history with the complete history from the server
      if (data.chatHistory) {
        setChatHistory(data.chatHistory);
      }
    } catch (error: any) {
      console.error('Error sending message to AI:', error);
      // Add error message to chat
      setChatHistory([
        ...chatHistory,
        {
          role: 'system',
          content: 'Sorry, there was an error processing your request. Please try again.',
          timestamp: new Date().toISOString()
        }
      ]);
    } finally {
      setIsSendingMessage(false);
    }
  };
  
  // Format chat timestamp
  const formatChatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      return '';
    }
  };
  
  // Handle chat history change
  const handleChatHistoryChange = (newChatHistory: any[]) => {
    if (Array.isArray(newChatHistory)) {
      setChatHistory(newChatHistory);
    } else {
      console.error('Invalid chat history format:', newChatHistory);
      setChatHistory([]);
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-t-4 border-primary-500 border-solid rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading proposal...</p>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="max-w-md p-8 bg-white shadow-lg rounded-lg">
          <div className="text-center">
            <svg className="w-16 h-16 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h1 className="mt-4 text-xl font-bold text-gray-900">Error</h1>
            <p className="mt-2 text-gray-600">{error}</p>
            <button
              onClick={() => router.push('/admin/proposals')}
              className="mt-4 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
            >
              Back to Proposals
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header - compact and fixed */}
      <div className="bg-white shadow flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                Edit Proposal: {formData.businessName}
              </h1>
              <p className="text-xs text-gray-500">
                Ref: {formData.referenceNumber} | Created: {proposal?.created_at && new Date(proposal.created_at).toLocaleDateString()}
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={toggleUiMode}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                {uiMode === 'split' ? 'Classic Mode' : 'Split-Screen Mode'}
              </button>

              <button
                onClick={() => window.open(`/view/proposals/${proposalId}`, '_blank')}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Preview
              </button>

              <button
                onClick={handleSubmit}
                disabled={saving}
                className={`px-4 py-1 text-sm rounded-md text-white ${
                  saving ? 'bg-gray-400' : 'bg-primary-600 hover:bg-primary-700'
                }`}
              >
                {saving ? 'Saving...' : 'Save'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content - use remaining height */}
      {uiMode === 'split' ? (
        // Split-screen editor - use remaining height properly
        <div className="flex-1 min-h-0">
          <SplitScreenEditor
            proposalId={proposalId}
            enhancedProposal={enhancedProposal || createDefaultEnhancedProposal('', '', '')}
            onUpdateProposal={handleUpdateProposal}
            chatHistory={chatHistory}
            onChatHistoryChange={handleChatHistoryChange}
            packages={packages}
            onPackagesChange={setPackages}
            nextSteps={nextSteps}
            onNextStepsChange={setNextSteps}
            formData={formData}
            onFormDataChange={handleFormDataChange}
            onSave={handleSubmit}
          />
        </div>
      ) : (
        // Classic editor mode - keep the existing UI implementation
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Keep the existing classic UI code here */}
          {/* This should include the original form, package editor, next steps editor, and AI chat */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Proposal Details</h2>
            <form onSubmit={handleSubmit}>
              {/* Basic Info Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* Form fields - client name, business name, etc. */}
                {/* ... */}
              </div>
              
              {/* Additional sections - packages, next steps, etc. */}
              {/* ... */}
              
              <div className="mt-6 flex justify-end">
                <button
                  type="submit"
                  disabled={saving}
                  className={`px-4 py-2 rounded-md text-white ${
                    saving ? 'bg-gray-400' : 'bg-primary-600 hover:bg-primary-700'
                  }`}
                >
                  {saving ? 'Saving...' : 'Save Proposal'}
                </button>
              </div>
            </form>
          </div>
          
          {/* AI Chat Section */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium text-gray-900">AI Proposal Assistant</h2>
              <button
                onClick={() => setShowAiChat(!showAiChat)}
                className="text-primary-600 hover:text-primary-800"
              >
                {showAiChat ? 'Hide Chat' : 'Show Chat'}
              </button>
            </div>
            
            {showAiChat && (
              <div className="border border-gray-200 rounded-lg h-96">
                <ProposalChatAssistant
                  proposalId={proposalId}
                  chatHistory={chatHistory}
                  onChatHistoryChange={handleChatHistoryChange}
                  proposalData={enhancedProposal || createDefaultEnhancedProposal('', '', '')}
                />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default function ProposalEditorPage() {
  const router = useRouter();
  
  useEffect(() => {
    const checkAccess = async () => {
      const adminAccess = await isAdmin();
      if (!adminAccess) {
        router.push('/dashboard');
      }
    };

    checkAccess();
  }, [router]);
  
  return (
    <DashboardLayout>
      <ProposalEditorContent />
    </DashboardLayout>
  );
} 