import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing Supabase connection...');
    
    const results: any = {
      timestamp: new Date().toISOString(),
      tests: []
    };
    
    // Test 1: Basic connection
    try {
      const { data, error } = await supabase
        .from('proposals')
        .select('count(*)')
        .limit(1);
        
      if (error) {
        results.tests.push({
          name: 'Basic Connection',
          status: 'FAILED',
          error: error.message,
          code: error.code
        });
      } else {
        results.tests.push({
          name: 'Basic Connection',
          status: 'PASSED',
          result: 'Connected to Supabase successfully'
        });
      }
    } catch (err: any) {
      results.tests.push({
        name: 'Basic Connection',
        status: 'ERROR',
        error: err.message
      });
    }
    
    // Test 2: Table structure
    try {
      const { data, error } = await supabase
        .from('proposals')
        .select('id, client_name, business_name, created_at')
        .limit(1);
        
      if (error) {
        results.tests.push({
          name: 'Table Structure',
          status: 'FAILED',
          error: error.message,
          code: error.code
        });
      } else {
        results.tests.push({
          name: 'Table Structure',
          status: 'PASSED',
          result: 'Table structure is correct'
        });
      }
    } catch (err: any) {
      results.tests.push({
        name: 'Table Structure',
        status: 'ERROR',
        error: err.message
      });
    }
    
    // Test 3: Insert test record (without auth)
    try {
      const testRecord = {
        client_name: 'Test Client',
        business_name: 'Test Business',
        business_type: 'test',
        proposal_data: {
          clientName: 'Test Client',
          businessName: 'Test Business',
          businessType: 'test'
        }
      };
      
      const { data, error } = await supabase
        .from('proposals')
        .insert(testRecord)
        .select()
        .single();
        
      if (error) {
        results.tests.push({
          name: 'Insert Test',
          status: 'FAILED',
          error: error.message,
          code: error.code,
          hint: error.code === '42501' ? 'RLS policy blocking insert - authentication required' : 'Unknown error'
        });
      } else {
        results.tests.push({
          name: 'Insert Test',
          status: 'PASSED',
          result: 'Successfully inserted test record',
          recordId: data.id
        });
        
        // Clean up test record
        await supabase
          .from('proposals')
          .delete()
          .eq('id', data.id);
      }
    } catch (err: any) {
      results.tests.push({
        name: 'Insert Test',
        status: 'ERROR',
        error: err.message
      });
    }
    
    // Test 4: Check RLS policies
    try {
      const { data, error } = await supabase.rpc('check_table_policies', {
        table_name: 'proposals'
      });
      
      results.tests.push({
        name: 'RLS Policies',
        status: error ? 'INFO' : 'PASSED',
        result: error ? 'Cannot check policies (function not available)' : 'Policies checked'
      });
    } catch (err: any) {
      results.tests.push({
        name: 'RLS Policies',
        status: 'INFO',
        result: 'Policy check function not available'
      });
    }
    
    const passedTests = results.tests.filter((t: any) => t.status === 'PASSED').length;
    const totalTests = results.tests.length;
    
    return NextResponse.json({
      success: passedTests > 0,
      summary: `${passedTests}/${totalTests} tests passed`,
      results,
      recommendations: [
        'If Insert Test failed with RLS error, you need to be authenticated',
        'Check that the proposals table exists in your Supabase database',
        'Verify RLS policies allow your operations',
        'Make sure environment variables are set correctly'
      ]
    });
    
  } catch (error: any) {
    console.error('Supabase test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Test failed',
      message: error.message
    }, { status: 500 });
  }
}
