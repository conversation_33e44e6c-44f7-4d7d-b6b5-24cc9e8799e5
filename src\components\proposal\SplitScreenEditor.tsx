import React, { useState, useEffect } from 'react';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';
import ProposalChatAssistant from '@/components/ai/ProposalChatAssistant';
import InteractiveProposalPreview from './InteractiveProposalPreview';
import {
  OverviewEditModal,
  ProjectOverviewEditModal,
  ClientGoalsEditModal,
  PackagesEditModal,
  NextStepsEditModal
} from './EditModals';

interface SplitScreenEditorProps {
  proposalId: string;
  enhancedProposal: EnhancedProposal;
  onUpdateProposal: (updatedProposal: EnhancedProposal) => void;
  chatHistory: any[];
  onChatHistoryChange: (history: any[]) => void;
  packages: any[];
  onPackagesChange: (packages: any[]) => void;
  nextSteps: string[];
  onNextStepsChange: (nextSteps: string[]) => void;
  formData: any;
  onFormDataChange: (formData: any) => void;
  onSave: () => Promise<void>;
}

const SplitScreenEditor: React.FC<SplitScreenEditorProps> = ({
  proposalId,
  enhancedProposal,
  onUpdateProposal,
  chatHistory,
  onChatHistoryChange,
  packages,
  onPackagesChange,
  nextSteps,
  onNextStepsChange,
  formData,
  onFormDataChange,
  onSave
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [previewScale, setPreviewScale] = useState(0.9);

  // Modal state management
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [modalData, setModalData] = useState<any>(null);
  
  // Handle input changes in the form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    onFormDataChange({ ...formData, [name]: value });
  };
  
  // Update the enhanced proposal when form data changes
  useEffect(() => {
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        clientName: formData.clientName,
        businessName: formData.businessName,
        businessType: formData.businessType,
      };
      
      // Update the overview if it exists
      if (updatedProposal.overview) {
        updatedProposal.overview = {
          ...updatedProposal.overview,
          projectTitle: `Digital Growth Proposal for ${formData.businessName}`
        };
      }
      
      onUpdateProposal(updatedProposal);
    }
  }, [formData.clientName, formData.businessName, formData.businessType]);
  
  // Handle package edits
  const handlePackageEdit = (index: number, field: string, value: any) => {
    const updatedPackages = [...packages];
    updatedPackages[index] = {
      ...updatedPackages[index],
      [field]: value
    };
    onPackagesChange(updatedPackages);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        packages: updatedPackages
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle feature edits
  const handleFeatureEdit = (packageIndex: number, featureIndex: number, value: string) => {
    const updatedPackages = [...packages];
    const features = [...updatedPackages[packageIndex].features];
    features[featureIndex] = value;
    updatedPackages[packageIndex] = {
      ...updatedPackages[packageIndex],
      features
    };
    onPackagesChange(updatedPackages);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        packages: updatedPackages
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle adding a feature
  const handleAddFeature = (packageIndex: number) => {
    const updatedPackages = [...packages];
    updatedPackages[packageIndex] = {
      ...updatedPackages[packageIndex],
      features: [...updatedPackages[packageIndex].features, '']
    };
    onPackagesChange(updatedPackages);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        packages: updatedPackages
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle removing a feature
  const handleRemoveFeature = (packageIndex: number, featureIndex: number) => {
    const updatedPackages = [...packages];
    const features = [...updatedPackages[packageIndex].features];
    features.splice(featureIndex, 1);
    updatedPackages[packageIndex] = {
      ...updatedPackages[packageIndex],
      features
    };
    onPackagesChange(updatedPackages);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        packages: updatedPackages
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle next step edits
  const handleNextStepEdit = (index: number, value: string) => {
    const updatedNextSteps = [...nextSteps];
    updatedNextSteps[index] = value;
    onNextStepsChange(updatedNextSteps);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        nextSteps: updatedNextSteps
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle adding a next step
  const handleAddNextStep = () => {
    const updatedNextSteps = [...nextSteps, ''];
    onNextStepsChange(updatedNextSteps);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        nextSteps: updatedNextSteps
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle removing a next step
  const handleRemoveNextStep = (index: number) => {
    const updatedNextSteps = [...nextSteps];
    updatedNextSteps.splice(index, 1);
    onNextStepsChange(updatedNextSteps);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        nextSteps: updatedNextSteps
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle saving the proposal
  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave();
    } finally {
      setIsSaving(false);
    }
  };

  // Handle section editing
  const handleEditSection = (section: string, data?: any) => {
    setActiveModal(section);
    setModalData(data);
  };

  // Handle modal save
  const handleModalSave = (section: string, data: any) => {
    switch (section) {
      case 'overview':
        onFormDataChange(data);
        break;
      case 'projectOverview':
        const updatedProposal = { ...enhancedProposal, ...data };
        onUpdateProposal(updatedProposal);
        break;
      case 'clientGoals':
        const updatedProposalGoals = { ...enhancedProposal, ...data };
        onUpdateProposal(updatedProposalGoals);
        break;
      case 'packages':
        onPackagesChange(data);
        const updatedProposalPackages = { ...enhancedProposal, packages: data };
        onUpdateProposal(updatedProposalPackages);
        break;
      case 'nextSteps':
        onNextStepsChange(data);
        const updatedProposalSteps = { ...enhancedProposal, nextSteps: data };
        onUpdateProposal(updatedProposalSteps);
        break;
    }
    setActiveModal(null);
    setModalData(null);
  };

  // Handle modal close
  const handleModalClose = () => {
    setActiveModal(null);
    setModalData(null);
  };
  
  // Update the preview scale when the slider is adjusted
  const handleScaleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newScale = parseFloat(e.target.value);
    setPreviewScale(newScale);
  };
  
  return (
    <div className="h-full flex flex-col">
      {/* Toolbar - simplified for click-to-edit mode */}
      <div className="bg-white border-b border-gray-200 py-2 px-4 flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <span className="text-sm text-gray-600">Click any section in the preview to edit</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Preview Scale:</span>
              <input
                type="range"
                min="0.4"
                max="0.9"
                step="0.1"
                value={previewScale}
                onChange={handleScaleChange}
                className="w-24"
              />
            </div>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className={`px-3 py-1 text-sm rounded-md text-white ${
                isSaving ? 'bg-gray-400' : 'bg-primary-600 hover:bg-primary-700'
              }`}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>

      {/* Main content area - two panels with proper height */}
      <div className="flex-1 flex min-h-0">
        {/* Left panel - Interactive Preview (2/3 width) */}
        <div className="w-2/3 border-r border-gray-200 bg-gray-100 overflow-y-auto preview-container">
          <div className="p-4">
            <InteractiveProposalPreview
              proposalData={enhancedProposal}
              packages={packages}
              nextSteps={nextSteps}
              formData={formData}
              onEditSection={handleEditSection}
              scale={previewScale}
            />
          </div>
        </div>
        
        {/* Right panel - AI Chat (1/3 width) */}
        <div className="w-1/3 h-full overflow-hidden">
          <ProposalChatAssistant
            proposalId={proposalId}
            chatHistory={chatHistory}
            onChatHistoryChange={onChatHistoryChange}
            proposalData={enhancedProposal}
            onProposalUpdate={(updatedData) => {
              console.log('Proposal updated by AI:', updatedData);
              // Update the enhanced proposal with AI changes
              const updatedProposal = { ...enhancedProposal, ...updatedData };
              onUpdateProposal(updatedProposal);

              // Also update packages and next steps if they were modified
              if (updatedData.packages) {
                onPackagesChange(updatedData.packages);
              }
              if (updatedData.nextSteps) {
                onNextStepsChange(updatedData.nextSteps);
              }
            }}
          />
        </div>
      </div>

      {/* Edit Modals */}
      <OverviewEditModal
        isOpen={activeModal === 'overview'}
        onClose={handleModalClose}
        onSave={(data) => handleModalSave('overview', data)}
        title="Edit Client Information"
        formData={formData}
      />

      <ProjectOverviewEditModal
        isOpen={activeModal === 'projectOverview'}
        onClose={handleModalClose}
        onSave={(data) => handleModalSave('projectOverview', data)}
        title="Edit Project Overview"
        proposalData={modalData || enhancedProposal}
      />

      <ClientGoalsEditModal
        isOpen={activeModal === 'clientGoals'}
        onClose={handleModalClose}
        onSave={(data) => handleModalSave('clientGoals', data)}
        title="Edit Client Goals"
        proposalData={modalData || enhancedProposal}
      />

      <PackagesEditModal
        isOpen={activeModal === 'packages'}
        onClose={handleModalClose}
        onSave={(data) => handleModalSave('packages', data)}
        title="Edit Packages"
        packages={packages}
      />

      <NextStepsEditModal
        isOpen={activeModal === 'nextSteps'}
        onClose={handleModalClose}
        onSave={(data) => handleModalSave('nextSteps', data)}
        title="Edit Next Steps"
        nextSteps={nextSteps}
      />
    </div>
  );
};

export default SplitScreenEditor; 