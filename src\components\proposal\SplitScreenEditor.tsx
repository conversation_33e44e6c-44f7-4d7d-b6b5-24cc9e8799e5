import React, { useState, useEffect } from 'react';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';
import ProposalChatAssistant from '@/components/ai/ProposalChatAssistant';

interface SplitScreenEditorProps {
  proposalId: string;
  enhancedProposal: EnhancedProposal;
  onUpdateProposal: (updatedProposal: EnhancedProposal) => void;
  chatHistory: any[];
  onChatHistoryChange: (history: any[]) => void;
  packages: any[];
  onPackagesChange: (packages: any[]) => void;
  nextSteps: string[];
  onNextStepsChange: (nextSteps: string[]) => void;
  formData: any;
  onFormDataChange: (formData: any) => void;
  onSave: () => Promise<void>;
}

const SplitScreenEditor: React.FC<SplitScreenEditorProps> = ({
  proposalId,
  enhancedProposal,
  onUpdateProposal,
  chatHistory,
  onChatHistoryChange,
  packages,
  onPackagesChange,
  nextSteps,
  onNextStepsChange,
  formData,
  onFormDataChange,
  onSave
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [previewScale, setPreviewScale] = useState(0.6);
  const [iframeHeight, setIframeHeight] = useState('100%');
  const [iframeWidth, setIframeWidth] = useState('100%');
  
  // Handle input changes in the form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    onFormDataChange({ ...formData, [name]: value });
  };
  
  // Update the enhanced proposal when form data changes
  useEffect(() => {
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        clientName: formData.clientName,
        businessName: formData.businessName,
        businessType: formData.businessType,
      };
      
      // Update the overview if it exists
      if (updatedProposal.overview) {
        updatedProposal.overview = {
          ...updatedProposal.overview,
          projectTitle: `Digital Growth Proposal for ${formData.businessName}`
        };
      }
      
      onUpdateProposal(updatedProposal);
    }
  }, [formData.clientName, formData.businessName, formData.businessType]);
  
  // Handle package edits
  const handlePackageEdit = (index: number, field: string, value: any) => {
    const updatedPackages = [...packages];
    updatedPackages[index] = {
      ...updatedPackages[index],
      [field]: value
    };
    onPackagesChange(updatedPackages);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        packages: updatedPackages
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle feature edits
  const handleFeatureEdit = (packageIndex: number, featureIndex: number, value: string) => {
    const updatedPackages = [...packages];
    const features = [...updatedPackages[packageIndex].features];
    features[featureIndex] = value;
    updatedPackages[packageIndex] = {
      ...updatedPackages[packageIndex],
      features
    };
    onPackagesChange(updatedPackages);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        packages: updatedPackages
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle adding a feature
  const handleAddFeature = (packageIndex: number) => {
    const updatedPackages = [...packages];
    updatedPackages[packageIndex] = {
      ...updatedPackages[packageIndex],
      features: [...updatedPackages[packageIndex].features, '']
    };
    onPackagesChange(updatedPackages);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        packages: updatedPackages
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle removing a feature
  const handleRemoveFeature = (packageIndex: number, featureIndex: number) => {
    const updatedPackages = [...packages];
    const features = [...updatedPackages[packageIndex].features];
    features.splice(featureIndex, 1);
    updatedPackages[packageIndex] = {
      ...updatedPackages[packageIndex],
      features
    };
    onPackagesChange(updatedPackages);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        packages: updatedPackages
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle next step edits
  const handleNextStepEdit = (index: number, value: string) => {
    const updatedNextSteps = [...nextSteps];
    updatedNextSteps[index] = value;
    onNextStepsChange(updatedNextSteps);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        nextSteps: updatedNextSteps
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle adding a next step
  const handleAddNextStep = () => {
    const updatedNextSteps = [...nextSteps, ''];
    onNextStepsChange(updatedNextSteps);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        nextSteps: updatedNextSteps
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle removing a next step
  const handleRemoveNextStep = (index: number) => {
    const updatedNextSteps = [...nextSteps];
    updatedNextSteps.splice(index, 1);
    onNextStepsChange(updatedNextSteps);
    
    // Also update in the enhanced proposal
    if (enhancedProposal) {
      const updatedProposal = {
        ...enhancedProposal,
        nextSteps: updatedNextSteps
      };
      onUpdateProposal(updatedProposal);
    }
  };
  
  // Handle saving the proposal
  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave();
    } finally {
      setIsSaving(false);
    }
  };
  
  // Generate iframe URL for preview
  const previewUrl = `/view/proposals/${proposalId}`;
  
  // Add a resize effect to better handle iframe dimensions
  useEffect(() => {
    // Calculate the best scale based on container width
    const calculateOptimalScale = () => {
      // The actual width of most proposal content is around 1200-1400px
      // We want to scale it to fit within our 1/3 panel
      const containerWidth = document.querySelector('.preview-container')?.clientWidth || 500;
      const proposalWidth = 1200; // Estimated width of the proposal content
      
      // Calculate a scale that would fit the proposal within the container
      const calculatedScale = containerWidth / proposalWidth;
      
      // Limit the scale within reasonable bounds
      return Math.max(0.4, Math.min(0.8, calculatedScale));
    };

    // Set initial scale
    const optimalScale = calculateOptimalScale();
    setPreviewScale(optimalScale);
    
    // Update iframe dimensions based on scale
    setIframeWidth(`${Math.floor(100 / optimalScale)}%`);
    setIframeHeight(`${Math.floor(100 / optimalScale)}vh`);
    
    // Set up a resize listener
    const handleResize = () => {
      const newScale = calculateOptimalScale();
      setPreviewScale(newScale);
      setIframeWidth(`${Math.floor(100 / newScale)}%`);
      setIframeHeight(`${Math.floor(100 / newScale)}vh`);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update the preview scale when the slider is adjusted
  const handleScaleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newScale = parseFloat(e.target.value);
    setPreviewScale(newScale);
    setIframeWidth(`${Math.floor(100 / newScale)}%`);
    setIframeHeight(`${Math.floor(100 / newScale)}vh`);
  };
  
  return (
    <div className="h-full flex flex-col">
      {/* Toolbar - compact and fixed */}
      <div className="bg-white border-b border-gray-200 py-2 px-4 flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-3 py-1 text-sm rounded ${
                activeTab === 'overview' ? 'bg-primary-100 text-primary-800' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('packages')}
              className={`px-3 py-1 text-sm rounded ${
                activeTab === 'packages' ? 'bg-primary-100 text-primary-800' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              Packages
            </button>
            <button
              onClick={() => setActiveTab('nextSteps')}
              className={`px-3 py-1 text-sm rounded ${
                activeTab === 'nextSteps' ? 'bg-primary-100 text-primary-800' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              Next Steps
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Preview Scale:</span>
              <input
                type="range"
                min="0.4"
                max="0.9"
                step="0.1"
                value={previewScale}
                onChange={handleScaleChange}
                className="w-24"
              />
            </div>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className={`px-3 py-1 text-sm rounded-md text-white ${
                isSaving ? 'bg-gray-400' : 'bg-primary-600 hover:bg-primary-700'
              }`}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>

      {/* Main content area - three panels with proper height */}
      <div className="flex-1 flex min-h-0">
        {/* Left panel - Preview */}
        <div className="w-1/3 border-r border-gray-200 bg-gray-100 overflow-hidden preview-container">
          <div className="flex justify-center h-full overflow-hidden">
            <div className="h-full" style={{ 
              transform: `scale(${previewScale})`, 
              transformOrigin: 'top center',
              width: '100%',
              overflow: 'hidden'
            }}>
              <iframe 
                src={previewUrl} 
                className="border-0"
                style={{ 
                  width: iframeWidth,
                  height: iframeHeight,
                  maxWidth: 'none',
                  maxHeight: 'none'
                }}
              />
            </div>
          </div>
        </div>
        
        {/* Middle panel - Editor */}
        <div className="w-1/3 border-r border-gray-200 overflow-y-auto p-4">
          {activeTab === 'overview' && (
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-gray-800">Proposal Overview</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Client Name</label>
                  <input
                    type="text"
                    name="clientName"
                    value={formData.clientName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
                  <input
                    type="text"
                    name="businessName"
                    value={formData.businessName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Business Type</label>
                  <select
                    name="businessType"
                    value={formData.businessType}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">Select business type</option>
                    <option value="plumber">Plumber</option>
                    <option value="electrician">Electrician</option>
                    <option value="landscaper">Landscaper</option>
                    <option value="painter">Painter</option>
                    <option value="hvac_technician">HVAC Technician</option>
                    <option value="general_contractor">General Contractor</option>
                    <option value="roofer">Roofer</option>
                    <option value="cleaning_service">Cleaning Service</option>
                    <option value="carpenter">Carpenter</option>
                    <option value="handyman">Handyman</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Challenges/Goals</label>
                  <textarea
                    name="challenges"
                    value={formData.challenges}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Describe the client's main challenges and goals..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Additional notes about the client or project..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Reference Number</label>
                  <input
                    type="text"
                    name="referenceNumber"
                    value={formData.referenceNumber}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="draft">Draft</option>
                    <option value="ready">Ready to Send</option>
                    <option value="sent">Sent</option>
                    <option value="accepted">Accepted</option>
                    <option value="declined">Declined</option>
                  </select>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'packages' && (
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-gray-800">Packages</h2>
              
              {packages.map((pkg, packageIndex) => (
                <div key={packageIndex} className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">{pkg.name}</h3>
                    <div className="flex items-center">
                      <label className="flex items-center space-x-2 mr-4">
                        <input
                          type="checkbox"
                          checked={pkg.isRecommended || false}
                          onChange={(e) => handlePackageEdit(packageIndex, 'isRecommended', e.target.checked)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <span className="text-sm">Recommended</span>
                      </label>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Package Name</label>
                      <input
                        type="text"
                        value={pkg.name}
                        onChange={(e) => handlePackageEdit(packageIndex, 'name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Setup Fee</label>
                        <input
                          type="text"
                          value={pkg.setupFee}
                          onChange={(e) => handlePackageEdit(packageIndex, 'setupFee', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Monthly Fee</label>
                        <input
                          type="text"
                          value={pkg.monthlyFee}
                          onChange={(e) => handlePackageEdit(packageIndex, 'monthlyFee', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Features
                      </label>
                      
                      {pkg.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center mb-2">
                          <input
                            type="text"
                            value={feature}
                            onChange={(e) => handleFeatureEdit(packageIndex, featureIndex, e.target.value)}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                          />
                          <button
                            type="button"
                            onClick={() => handleRemoveFeature(packageIndex, featureIndex)}
                            className="ml-2 text-red-500 hover:text-red-700"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                          </button>
                        </div>
                      ))}
                      
                      <button
                        type="button"
                        onClick={() => handleAddFeature(packageIndex)}
                        className="mt-2 flex items-center text-primary-600 hover:text-primary-800"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Feature
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {activeTab === 'nextSteps' && (
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-gray-800">Next Steps</h2>
              
              <div className="space-y-4">
                {nextSteps.map((step, index) => (
                  <div key={index} className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary-100 text-primary-600 flex items-center justify-center mr-3 mt-2">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <textarea
                        value={step}
                        onChange={(e) => handleNextStepEdit(index, e.target.value)}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveNextStep(index)}
                      className="ml-2 mt-2 text-red-500 hover:text-red-700"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>
                ))}
                
                <button
                  type="button"
                  onClick={handleAddNextStep}
                  className="flex items-center text-primary-600 hover:text-primary-800"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Add Next Step
                </button>
              </div>
            </div>
          )}
        </div>
        
        {/* Right panel - AI Chat */}
        <div className="w-1/3 h-full overflow-hidden">
          <ProposalChatAssistant
            proposalId={proposalId}
            chatHistory={chatHistory}
            onChatHistoryChange={onChatHistoryChange}
            proposalData={enhancedProposal}
          />
        </div>
      </div>
    </div>
  );
};

export default SplitScreenEditor; 