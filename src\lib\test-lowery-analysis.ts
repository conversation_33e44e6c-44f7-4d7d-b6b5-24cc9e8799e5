/**
 * Test script to demonstrate Lowery Finishing analysis
 * Run this to see the AI recommendation system in action
 */

import { analyzeLoweryFinishing, analyzeCustomerProfile } from './ai-proposal-analyzer';

export function testLoweryAnalysis() {
  console.log('🔍 ANALYZING LOWERY FINISHING...\n');
  
  const analysis = analyzeLoweryFinishing();
  
  console.log('📊 CUSTOMER PROFILE:');
  console.log('- Business: Lowery Finishing');
  console.log('- Industry: Door and trim installation');
  console.log('- Current Website: ClickFunnels landing page');
  console.log('- Target Markets: Homeowners + Builders');
  console.log('- Challenge: Needs credibility with builders');
  console.log('- Goal: Professional website for legitimacy\n');
  
  console.log('🎯 AI RECOMMENDATION:');
  console.log(`- Package: ${analysis.recommendedPackage.toUpperCase()}`);
  console.log(`- Confidence: ${Math.round(analysis.confidence * 100)}%\n`);
  
  console.log('💡 REASONING:');
  analysis.reasoning.forEach((reason, index) => {
    console.log(`${index + 1}. ${reason}`);
  });
  
  console.log('\n🔧 CUSTOMIZATIONS:');
  console.log('Overview:', analysis.customizations.overview);
  console.log('Client Goals:', analysis.customizations.clientGoals);
  console.log('Next Steps:', analysis.customizations.nextSteps);
  
  return analysis;
}

// Test with different business types
export function testDifferentBusinessTypes() {
  console.log('\n🧪 TESTING DIFFERENT BUSINESS TYPES...\n');
  
  const testCases = [
    {
      name: 'Startup Bakery',
      profile: {
        businessName: 'Sweet Dreams Bakery',
        businessType: 'bakery',
        hasExistingOnlinePresence: false,
        hasGoogleReviews: false,
        isEstablishedBusiness: false,
        challenges: 'need to get found online',
        goals: 'attract local customers'
      }
    },
    {
      name: 'Established Law Firm',
      profile: {
        businessName: 'Johnson & Associates',
        businessType: 'law firm',
        hasExistingOnlinePresence: true,
        hasGoogleReviews: true,
        isEstablishedBusiness: true,
        targetMarkets: ['personal injury clients', 'corporate clients'],
        challenges: 'need better online credibility',
        goals: 'rank for specific legal services'
      }
    },
    {
      name: 'Home Services Contractor',
      profile: {
        businessName: 'Elite Roofing',
        businessType: 'roofing contractor',
        hasExistingOnlinePresence: true,
        hasGoogleReviews: true,
        isEstablishedBusiness: true,
        targetMarkets: ['homeowners', 'insurance companies'],
        challenges: 'competition in local market',
        goals: 'rank higher for roofing services'
      }
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n--- ${testCase.name} ---`);
    const analysis = analyzeCustomerProfile(testCase.profile as any);
    console.log(`Recommendation: ${analysis.recommendedPackage.toUpperCase()} (${Math.round(analysis.confidence * 100)}%)`);
    console.log(`Key Reason: ${analysis.reasoning[0]}`);
  });
}

// Run the tests
if (typeof window === 'undefined') {
  // Only run in Node.js environment
  testLoweryAnalysis();
  testDifferentBusinessTypes();
}
