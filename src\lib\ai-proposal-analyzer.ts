/**
 * AI-powered proposal analysis and customization service
 * Analyzes customer information to recommend packages and customize content
 */

export interface CustomerProfile {
  businessName: string;
  businessType: string;
  currentWebsite?: string;
  targetMarkets?: string[];
  challenges?: string;
  goals?: string;
  hasExistingOnlinePresence?: boolean;
  hasGoogleReviews?: boolean;
  isEstablishedBusiness?: boolean;
}

export interface PackageRecommendation {
  recommendedPackage: 'basic' | 'premium';
  confidence: number; // 0-1
  reasoning: string[];
  customizations: {
    overview?: string;
    clientGoals?: string[];
    specificBenefits?: string[];
    nextSteps?: string[];
  };
}

export interface ProposalCustomization {
  overview: {
    description: string;
    keyPoints: string[];
  };
  clientGoals: {
    primary: string[];
    secondary: string[];
  };
  packages: {
    recommended: string;
    customFeatures?: string[];
  };
  nextSteps: string[];
  timeline?: string;
}

/**
 * Analyzes customer profile and recommends appropriate package
 */
export function analyzeCustomerProfile(profile: CustomerProfile): PackageRecommendation {
  const factors = {
    hasWebsite: !!profile.currentWebsite,
    isEstablished: profile.isEstablishedBusiness || profile.hasGoogleReviews,
    multipleMarkets: (profile.targetMarkets?.length || 0) > 1,
    needsCredibility: profile.challenges?.toLowerCase().includes('credibility') || 
                     profile.challenges?.toLowerCase().includes('professional'),
    needsSEO: profile.goals?.toLowerCase().includes('rank') || 
              profile.goals?.toLowerCase().includes('seo') ||
              profile.businessType?.toLowerCase().includes('local'),
    complexNeeds: profile.targetMarkets?.some(market => 
      market.toLowerCase().includes('builder') || 
      market.toLowerCase().includes('contractor')
    )
  };

  // Scoring system for package recommendation
  let premiumScore = 0;
  const reasoning: string[] = [];

  // Established business indicators
  if (factors.isEstablished) {
    premiumScore += 0.3;
    reasoning.push("Established business with existing reputation benefits from advanced SEO and professional presence");
  }

  // Multiple target markets
  if (factors.multipleMarkets) {
    premiumScore += 0.25;
    reasoning.push("Multiple target markets require dedicated service pages and advanced lead generation");
  }

  // Professional credibility needs
  if (factors.needsCredibility) {
    premiumScore += 0.2;
    reasoning.push("Professional credibility requirements indicate need for comprehensive website and SEO strategy");
  }

  // Complex business needs (B2B components)
  if (factors.complexNeeds) {
    premiumScore += 0.15;
    reasoning.push("B2B market focus requires advanced features and professional presentation");
  }

  // SEO and ranking needs
  if (factors.needsSEO) {
    premiumScore += 0.1;
    reasoning.push("SEO and ranking goals align with Premium package capabilities");
  }

  const recommendedPackage = premiumScore >= 0.5 ? 'premium' : 'basic';
  
  // Generate customizations based on profile
  const customizations = generateCustomizations(profile, recommendedPackage);

  return {
    recommendedPackage,
    confidence: Math.min(premiumScore, 0.95), // Cap at 95%
    reasoning,
    customizations
  };
}

/**
 * Generates customized content based on customer profile
 */
function generateCustomizations(profile: CustomerProfile, packageType: 'basic' | 'premium') {
  const customizations: any = {};

  // Customize overview based on business type and goals
  if (profile.businessType && profile.businessName) {
    customizations.overview = `Our team at GetFound is excited to help transform ${profile.businessName}'s online presence. As specialists working with ${profile.businessType} businesses, we understand the unique challenges you face in attracting and converting the right customers.`;
    
    if (profile.targetMarkets && profile.targetMarkets.length > 1) {
      customizations.overview += ` With your dual focus on ${profile.targetMarkets.join(' and ')}, you need a sophisticated online strategy that speaks to each market segment effectively.`;
    }
  }

  // Customize client goals based on challenges and target markets
  customizations.clientGoals = [];
  
  if (profile.challenges?.toLowerCase().includes('credibility')) {
    customizations.clientGoals.push("Establish professional credibility and trust with potential clients");
  }
  
  if (profile.targetMarkets?.some(market => market.toLowerCase().includes('builder'))) {
    customizations.clientGoals.push("Build trust and credibility with builders and contractors");
    customizations.clientGoals.push("Showcase expertise and reliability for B2B partnerships");
  }
  
  if (profile.targetMarkets?.some(market => market.toLowerCase().includes('homeowner'))) {
    customizations.clientGoals.push("Attract and convert homeowners seeking quality services");
    customizations.clientGoals.push("Simplify the quote request process for residential customers");
  }

  // Customize specific benefits based on package and needs
  customizations.specificBenefits = [];
  
  if (packageType === 'premium') {
    customizations.specificBenefits.push("Dedicated service pages for each target market");
    customizations.specificBenefits.push("Advanced lead generation and quote request system");
    customizations.specificBenefits.push("Professional portfolio showcasing past projects");
    
    if (profile.businessType?.toLowerCase().includes('door') || 
        profile.businessType?.toLowerCase().includes('installation')) {
      customizations.specificBenefits.push("Before/after project galleries to showcase quality work");
      customizations.specificBenefits.push("Service area mapping for local SEO optimization");
    }
  }

  // Customize next steps based on business readiness
  customizations.nextSteps = [
    "Schedule a strategy call to discuss your specific market needs",
    `Select the ${packageType === 'premium' ? 'Premium' : 'Basic'} package that aligns with your growth goals`,
    "Provide business assets and content for website development",
    "Begin market-specific content creation and SEO optimization"
  ];

  return customizations;
}

/**
 * Analyzes Lowery Finishing as a case study
 */
export function analyzeLoweryFinishing(): PackageRecommendation {
  const loweryProfile: CustomerProfile = {
    businessName: "Lowery Finishing",
    businessType: "door and trim installation",
    currentWebsite: "ClickFunnels landing page",
    targetMarkets: [
      "homeowners seeking exterior door swaps with measurement system",
      "builders requiring comprehensive door and trim installation services"
    ],
    challenges: "needs credibility improvement with builders, wants legitimate website for easier quote requests",
    goals: "professional website for legitimacy and lead generation, easier quote system",
    hasExistingOnlinePresence: true,
    hasGoogleReviews: true,
    isEstablishedBusiness: true
  };

  return analyzeCustomerProfile(loweryProfile);
}

/**
 * Generates AI chat prompt for proposal customization
 */
export function generateAICustomizationPrompt(
  profile: CustomerProfile, 
  recommendation: PackageRecommendation,
  currentProposal: any
): string {
  return `You are an expert proposal writer helping customize a business proposal. 

CUSTOMER PROFILE:
- Business: ${profile.businessName}
- Industry: ${profile.businessType}
- Target Markets: ${profile.targetMarkets?.join(', ') || 'Not specified'}
- Current Challenges: ${profile.challenges || 'Not specified'}
- Goals: ${profile.goals || 'Not specified'}

PACKAGE RECOMMENDATION: ${recommendation.recommendedPackage.toUpperCase()}
Confidence: ${Math.round(recommendation.confidence * 100)}%

REASONING:
${recommendation.reasoning.map(r => `- ${r}`).join('\n')}

CURRENT PROPOSAL SECTIONS:
- Overview: ${currentProposal.overview?.description || 'Not set'}
- Client Goals: ${currentProposal.clientGoals?.primary?.join(', ') || 'Not set'}
- Recommended Package: ${currentProposal.packages?.recommended || 'Not set'}

Please help customize this proposal to better match the customer's specific needs. You can:
1. Update the overview to be more relevant to their business
2. Modify client goals to reflect their actual challenges
3. Adjust package recommendations with specific reasoning
4. Customize next steps for their situation

When the user asks for changes, provide specific, actionable updates to the proposal content.`;
}
