import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// TEMPORARY API ENDPOINT FOR TESTING - BYPASSES AUTHENTICATION
// This should be removed once authentication is working properly

export async function GET() {
  try {
    console.log('Testing proposals list without authentication...');
    
    // Try to get proposals directly without authentication
    const { data, error } = await supabase
      .from('proposals')
      .select('id, client_name, business_name, reference_number, created_at, updated_at')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json({ 
        error: 'Failed to retrieve proposals from database',
        message: error.message,
        code: error.code,
        hint: error.code === '42501' ? 'RLS policy blocking access - table requires authentication' : 'Database error'
      }, { status: 500 });
    }
    
    console.log(`Found ${data?.length || 0} proposals`);
    
    return NextResponse.json({ 
      success: true,
      proposals: data || [],
      count: data?.length || 0,
      message: 'Retrieved proposals without authentication (testing mode)'
    });
    
  } catch (error: any) {
    console.error('Error retrieving proposals:', error);
    return NextResponse.json({ 
      error: 'Failed to retrieve proposals',
      message: error.message
    }, { status: 500 });
  }
}
