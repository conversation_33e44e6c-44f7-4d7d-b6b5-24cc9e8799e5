import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// DEVELOPMENT ONLY - Auto-login endpoint for testing
// This should be removed in production

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Auto-login with the known user
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: process.env.DEV_PASSWORD || 'your_password_here', // Add this to .env.local
    });

    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
        hint: 'Make sure DEV_PASSWORD is set in .env.local'
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: 'Logged in successfully',
      user: {
        id: data.user?.id,
        email: data.user?.email
      },
      session: {
        expires_at: data.session?.expires_at
      }
    });

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Development login endpoint. Use POST to auto-login.',
    instructions: [
      '1. Add DEV_PASSWORD=your_actual_password to .env.local',
      '2. POST to this endpoint to auto-login',
      '3. Remove this endpoint in production'
    ]
  });
}
