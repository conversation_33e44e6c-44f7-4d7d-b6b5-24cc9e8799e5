/**
 * Customer Analysis Input Component
 * Allows pasting customer information for AI analysis and proposal generation
 */

import React, { useState } from 'react';
import { generateDynamicProposal, DynamicProposalContent } from '@/lib/dynamic-proposal-generator';

interface CustomerAnalysisInputProps {
  onAnalysisComplete: (proposalContent: DynamicProposalContent) => void;
  onClose: () => void;
}

const CustomerAnalysisInput: React.FC<CustomerAnalysisInputProps> = ({
  onAnalysisComplete,
  onClose
}) => {
  const [customerInfo, setCustomerInfo] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<DynamicProposalContent | null>(null);

  const handleAnalyze = async () => {
    if (!customerInfo.trim()) return;
    
    setIsAnalyzing(true);
    
    try {
      // Simulate AI processing delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const proposalContent = generateDynamicProposal(customerInfo);
      setAnalysisResult(proposalContent);
    } catch (error) {
      console.error('Error analyzing customer info:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleApplyToProposal = () => {
    if (analysisResult) {
      onAnalysisComplete(analysisResult);
      onClose();
    }
  };

  const loadLoweryExample = () => {
    setCustomerInfo(`Business: Lowery Finishing
Industry: Door and trim installation
Current Website: ClickFunnels landing page
Target Markets: 
- Homeowners seeking exterior door swaps with measurement system
- Builders requiring comprehensive door and trim installation services

Key Goals:
- Professional "legit" website that showcases excellent craftsmanship
- Streamlined quote requests, especially for exterior door swaps
- Enhanced Google Business Profile for better visibility and credibility
- Build credibility with builders for high-value commercial work
- Efficient remote estimation process for door swaps

Current Challenges:
- Moving beyond basic ClickFunnels page to robust, credible website
- Need better system for exterior door swap estimates
- Want to showcase credibility with builders
- Improve Google listing (issues with automatic photo display)

Services Offered:
- Exterior Door Swaps (homeowner focus)
- Interior Door Installation (builder focus)
- Pocket, Bi-Fold & Barn Door Installation
- Trim & Millwork (Base, Casing, Window Surrounds, Sills, Mulls, Skirt Boards, Wall Caps)
- Custom Built-Ins (Bookcases, Benches, Desks, Lockers, Mantels)
- Staircases (Treads, Risers, Handrails, Newel Posts)
- Closet Systems (Shelving - Standard & Face Framed)`);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              🤖 AI Customer Analysis & Proposal Generator
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="space-y-6">
            {/* Input Section */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  Customer Information & Requirements
                </label>
                <button
                  onClick={loadLoweryExample}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  📋 Load Lowery Finishing Example
                </button>
              </div>
              <textarea
                value={customerInfo}
                onChange={(e) => setCustomerInfo(e.target.value)}
                placeholder="Paste customer information, goals, challenges, and requirements here..."
                className="w-full h-48 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-sm text-gray-500 mt-2">
                Include business details, target markets, goals, challenges, and any specific requirements.
              </p>
            </div>

            {/* Analyze Button */}
            <div className="flex justify-center">
              <button
                onClick={handleAnalyze}
                disabled={!customerInfo.trim() || isAnalyzing}
                className={`px-6 py-3 rounded-lg font-medium ${
                  !customerInfo.trim() || isAnalyzing
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isAnalyzing ? (
                  <>
                    <svg className="w-5 h-5 animate-spin inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Analyzing Customer...
                  </>
                ) : (
                  '🔍 Analyze & Generate Proposal'
                )}
              </button>
            </div>

            {/* Analysis Results */}
            {analysisResult && (
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  📊 Analysis Results & Proposal Preview
                </h3>
                
                <div className="space-y-4">
                  {/* Package Recommendation */}
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">
                      🎯 Recommended Package: {analysisResult.packageRecommendation.recommended.toUpperCase()}
                    </h4>
                    <p className="text-blue-800 text-sm mb-2">
                      {analysisResult.packageRecommendation.reasoning}
                    </p>
                    <ul className="text-blue-700 text-sm space-y-1">
                      {analysisResult.packageRecommendation.whyThisPackage.map((reason, index) => (
                        <li key={index}>• {reason}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Generated Content Preview */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-3">📝 Generated Proposal Content:</h4>
                    
                    <div className="space-y-3 text-sm">
                      <div>
                        <strong>Project Title:</strong>
                        <p className="text-gray-700">{analysisResult.projectTitle}</p>
                      </div>
                      
                      <div>
                        <strong>Client Greeting:</strong>
                        <p className="text-gray-700">{analysisResult.clientGreeting}</p>
                      </div>
                      
                      <div>
                        <strong>Situation Analysis:</strong>
                        <p className="text-gray-700">{analysisResult.situationAnalysis}</p>
                      </div>
                      
                      <div>
                        <strong>Key Goals:</strong>
                        <p className="text-gray-700 whitespace-pre-line">{analysisResult.goalsSection}</p>
                      </div>
                      
                      <div>
                        <strong>Specific Solutions:</strong>
                        <ul className="text-gray-700 space-y-1">
                          {analysisResult.specificSolutions.map((solution, index) => (
                            <li key={index}>• {solution}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Apply Button */}
                  <div className="flex justify-center pt-4">
                    <button
                      onClick={handleApplyToProposal}
                      className="px-8 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700"
                    >
                      ✅ Apply to Proposal Preview
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerAnalysisInput;
