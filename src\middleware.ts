import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();

  // Create a Supabase client configured to use cookies
  const supabase = createMiddlewareClient({ req, res });

  // Refresh session if expired - required for Server Components
  const { data: { session }, error } = await supabase.auth.getSession();

  // Log session status for debugging
  if (req.nextUrl.pathname.startsWith('/admin') || req.nextUrl.pathname.startsWith('/api')) {
    console.log(`[Middleware] ${req.nextUrl.pathname} - Session: ${session ? 'EXISTS' : 'MISSING'}`);
    if (error) {
      console.log(`[Middleware] Auth error:`, error.message);
    }
  }

  return res;
}

// This ensures our middleware runs on the appropriate routes
export const config = {
  matcher: [
    // Apply this middleware to all admin routes and API routes
    '/admin/:path*',
    '/api/:path*',
    '/debug/:path*',
  ],
}; 